import cv2
import numpy as np
from utils.config import Config

class CharacterSegmenter:
    """字符分割器類"""
    
    def __init__(self):
        self.min_char_width = Config.CHAR_WIDTH_MIN
        self.min_char_height = Config.CHAR_HEIGHT_MIN
        
    def segment_characters(self, plate_image):
        """分割車牌中的字符"""
        try:
            # 預處理車牌圖像
            processed = self._preprocess_plate(plate_image)
            
            # 嘗試不同的分割方法
            methods = [
                self._vertical_projection_split,
                self._connected_components_split,
                self._contour_based_split
            ]
            
            characters = []
            for method in methods:
                try:
                    chars = method(processed.copy())
                    if len(chars) >= 4:  # 至少要有4個字符
                        characters = chars
                        break
                except Exception as e:
                    print(f"分割方法 {method.__name__} 失敗: {e}")
                    continue
            
            # 驗證分割結果
            if self._validate_segments(characters):
                return characters
            else:
                return self._fallback_segmentation(processed)
                
        except Exception as e:
            print(f"字符分割出錯: {e}")
            return []
    
    def _preprocess_plate(self, plate_image):
        """預處理車牌圖像"""
        # 轉換為灰度
        if len(plate_image.shape) == 3:
            gray = cv2.cvtColor(plate_image, cv2.COLOR_BGR2GRAY)
        else:
            gray = plate_image.copy()
        
        # 調整大小
        height, width = gray.shape
        if height < 50:
            scale_factor = 50 / height
            new_width = int(width * scale_factor)
            gray = cv2.resize(gray, (new_width, 50), interpolation=cv2.INTER_LINEAR)
        
        # 應用自適應閾值
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY_INV, 11, 2)
        
        # 形態學操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return binary
    
    def _vertical_projection_split(self, binary_image):
        """基於垂直投影的字符分割"""
        # 計算垂直投影
        vertical_proj = np.sum(binary_image, axis=0)
        
        # 找到字符邊界
        height, width = binary_image.shape
        min_projection = height * 0.1  # 最小投影值
        
        boundaries = []
        in_char = False
        start = 0
        
        for i, proj in enumerate(vertical_proj):
            if proj > min_projection and not in_char:
                # 字符開始
                start = i
                in_char = True
            elif proj <= min_projection and in_char:
                # 字符結束
                if i - start >= self.min_char_width:
                    boundaries.append((start, i))
                in_char = False
        
        # 處理最後一個字符
        if in_char and width - start >= self.min_char_width:
            boundaries.append((start, width))
        
        # 提取字符
        characters = []
        for start, end in boundaries:
            char_img = binary_image[:, start:end]
            characters.append(char_img)
        
        return characters
    
    def _connected_components_split(self, binary_image):
        """基於連通域的字符分割"""
        # 查找連通域
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(
            binary_image, connectivity=8)
        
        # 過濾小的連通域
        valid_components = []
        for i in range(1, num_labels):  # 跳過背景
            x, y, w, h, area = stats[i]
            
            if (w >= self.min_char_width and 
                h >= self.min_char_height and 
                area >= 100):
                valid_components.append({
                    'x': x, 'y': y, 'w': w, 'h': h,
                    'area': area, 'centroid': centroids[i]
                })
        
        # 按x坐標排序
        valid_components.sort(key=lambda x: x['x'])
        
        # 提取字符
        characters = []
        for comp in valid_components:
            char_img = binary_image[comp['y']:comp['y']+comp['h'],
                                   comp['x']:comp['x']+comp['w']]
            characters.append(char_img)
        
        return characters
    
    def _contour_based_split(self, binary_image):
        """基於輪廓的字符分割"""
        # 查找輪廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL,
                                       cv2.CHAIN_APPROX_SIMPLE)
        
        # 過濾輪廓
        valid_contours = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            area = cv2.contourArea(contour)
            
            if (w >= self.min_char_width and 
                h >= self.min_char_height and 
                area >= 100):
                valid_contours.append({
                    'x': x, 'y': y, 'w': w, 'h': h, 'area': area
                })
        
        # 按x坐標排序
        valid_contours.sort(key=lambda x: x['x'])
        
        # 提取字符
        characters = []
        for contour in valid_contours:
            char_img = binary_image[contour['y']:contour['y']+contour['h'],
                                   contour['x']:contour['x']+contour['w']]
            characters.append(char_img)
        
        return characters
    
    def _validate_segments(self, characters):
        """驗證分割結果"""
        if len(characters) < 4:  # 台灣車牌至少有4個字符
            return False
        
        if len(characters) > 8:  # 最多8個字符
            return False
        
        # 檢查每個字符的尺寸
        for char in characters:
            h, w = char.shape
            if w < self.min_char_width or h < self.min_char_height:
                return False
            
            # 檢查寬高比
            aspect_ratio = w / h
            if aspect_ratio < 0.2 or aspect_ratio > 1.5:
                return False
        
        return True
    
    def _fallback_segmentation(self, binary_image):
        """備用分割方法"""
        # 簡單的等寬分割
        height, width = binary_image.shape
        num_chars = 6  # 假設6個字符
        char_width = width // num_chars
        
        characters = []
        for i in range(num_chars):
            start_x = i * char_width
            end_x = (i + 1) * char_width
            
            char_img = binary_image[:, start_x:end_x]
            characters.append(char_img)
        
        return characters
    
    def normalize_characters(self, characters, target_size=(32, 32)):
        """標準化字符尺寸"""
        normalized = []
        
        for char in characters:
            # 調整大小
            resized = cv2.resize(char, target_size, interpolation=cv2.INTER_LINEAR)
            
            # 確保是二值圖像
            _, binary = cv2.threshold(resized, 127, 255, cv2.THRESH_BINARY)
            
            normalized.append(binary)
        
        return normalized
    
    def get_segmentation_info(self, characters):
        """獲取分割信息"""
        info = {
            'total_characters': len(characters),
            'character_sizes': [],
            'average_width': 0,
            'average_height': 0
        }
        
        if not characters:
            return info
        
        total_width = 0
        total_height = 0
        
        for i, char in enumerate(characters):
            h, w = char.shape
            info['character_sizes'].append({
                'index': i,
                'width': w,
                'height': h,
                'aspect_ratio': w / h
            })
            total_width += w
            total_height += h
        
        info['average_width'] = total_width / len(characters)
        info['average_height'] = total_height / len(characters)
        
        return info