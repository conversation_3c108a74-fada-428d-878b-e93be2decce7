# 測試備用檢測方法
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import cv2
import numpy as np
from core.preprocessor import ImagePreprocessor
from core.detector import LicensePlateDetector
from core.segmenter import CharacterSegmenter
from core.recognizer import CharacterRecognizer

def test_fallback_detection():
    """測試備用檢測方法"""
    print("測試備用車牌檢測方法...")
    
    # 初始化各個模組
    preprocessor = ImagePreprocessor()
    detector = LicensePlateDetector()
    segmenter = CharacterSegmenter()
    recognizer = CharacterRecognizer()
    
    # 測試圖像
    test_images = [
        "test_samples/plate_closeup.jpg",
        "test_samples/realistic_car.jpg", 
        "test_samples/perspective_plate.jpg"
    ]
    
    for image_path in test_images:
        if not os.path.exists(image_path):
            print(f"文件不存在: {image_path}")
            continue
            
        print(f"\n處理: {image_path}")
        
        try:
            # 載入圖像
            image = cv2.imread(image_path)
            if image is None:
                print(f"  無法載入圖像")
                continue
            
            # 使用備用檢測方法
            print("  使用備用檢測方法...")
            
            # 轉換為灰度
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 應用高斯模糊
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # 邊緣檢測
            edges = cv2.Canny(blurred, 50, 150)
            
            # 查找輪廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            print(f"  找到 {len(contours)} 個輪廓")
            
            # 過濾可能的車牌區域
            potential_plates = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                
                # 檢查尺寸
                if w < 100 or h < 30:
                    continue
                
                # 檢查寬高比
                aspect_ratio = w / h
                if aspect_ratio < 2.0 or aspect_ratio > 5.0:
                    continue
                
                # 計算面積
                area = cv2.contourArea(contour)
                if area < 1000:
                    continue
                
                potential_plates.append({
                    'bbox': (x, y, x + w, y + h),
                    'area': area,
                    'aspect_ratio': aspect_ratio
                })
                
                # 在圖像上繪製邊界框
                cv2.rectangle(image, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            print(f"  找到 {len(potential_plates)} 個潛在車牌區域")
            
            if potential_plates:
                # 選擇最大的區域
                best_plate = max(potential_plates, key=lambda x: x['area'])
                x1, y1, x2, y2 = best_plate['bbox']
                
                print(f"  最佳車牌區域: {best_plate['bbox']}")
                print(f"  面積: {best_plate['area']}")
                print(f"  寬高比: {best_plate['aspect_ratio']:.2f}")
                
                # 提取車牌區域
                plate_region = image[y1:y2, x1:x2]
                
                # 預處理車牌
                processed_plate = preprocessor.preprocess_for_ocr(plate_region)
                
                # 字符分割
                characters = segmenter.segment_characters(processed_plate)
                print(f"  分割出 {len(characters)} 個字符")
                
                if characters:
                    # 字符識別
                    recognition_results = recognizer.recognize_characters(characters)
                    plate_text = recognizer.combine_characters(recognition_results)
                    
                    print(f"  識別結果: {plate_text}")
                    
                    # 驗證格式
                    is_valid = recognizer.validate_plate_format(plate_text)
                    print(f"  格式驗證: {'通過' if is_valid else '未通過'}")
                
                # 保存結果圖像
                result_path = image_path.replace('.jpg', '_detected.jpg')
                cv2.imwrite(result_path, image)
                print(f"  結果圖像保存: {result_path}")
            else:
                print("  未找到潛在的車牌區域")
                
        except Exception as e:
            print(f"  處理錯誤: {e}")

if __name__ == "__main__":
    test_fallback_detection()