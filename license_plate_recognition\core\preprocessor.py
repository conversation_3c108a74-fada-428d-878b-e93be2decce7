import cv2
import numpy as np
from utils.image_utils import *
from utils.config import Config

class ImagePreprocessor:
    """圖像預處理器類"""
    
    def __init__(self, target_size=Config.IMAGE_SIZE):
        self.target_size = target_size
        
    def preprocess_for_detection(self, image):
        """為車牌檢測預處理圖像"""
        # 調整尺寸
        processed = resize_image(image, self.target_size)
        
        # 增強圖像
        processed = enhance_image(processed)
        
        # 降噪
        processed = denoise_image(processed)
        
        return processed
    
    def preprocess_for_ocr(self, plate_image):
        """為OCR識別預處理車牌圖像"""
        # 轉換為灰度
        gray = convert_to_grayscale(plate_image)
        
        # 應用自適應閾值
        binary = apply_threshold(gray, method='adaptive')
        
        # 形態學操作去除噪點
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)
        
        # 邊緣增強
        edges = cv2.Canny(cleaned, 50, 150)
        enhanced = cv2.bitwise_or(cleaned, edges)
        
        return enhanced
    
    def correct_perspective(self, image, corners):
        """透視校正"""
        # 定義目標尺寸
        width, height = 300, 100
        
        # 來源和目標點
        src_points = np.array(corners, dtype=np.float32)
        dst_points = np.array([
            [0, 0],
            [width - 1, 0],
            [width - 1, height - 1],
            [0, height - 1]
        ], dtype=np.float32)
        
        # 計算透視變換矩陣
        matrix = cv2.getPerspectiveTransform(src_points, dst_points)
        
        # 應用透視校正
        corrected = cv2.warpPerspective(image, matrix, (width, height))
        
        return corrected
    
    def enhance_plate_region(self, plate_image):
        """增強車牌區域"""
        # 調整對比度和亮度
        enhanced = adjust_brightness_contrast(plate_image, contrast=30, brightness=10)
        
        # 銳化處理
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)
        
        return sharpened
    
    def remove_shadow(self, image):
        """去除陰影"""
        # 分離RGB通道
        b, g, r = cv2.split(image)
        
        # 對每個通道應用形態學操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
        
        b_no_shadow = self._remove_channel_shadow(b, kernel)
        g_no_shadow = self._remove_channel_shadow(g, kernel)
        r_no_shadow = self._remove_channel_shadow(r, kernel)
        
        # 合併通道
        return cv2.merge([b_no_shadow, g_no_shadow, r_no_shadow])
    
    def _remove_channel_shadow(self, channel, kernel):
        """去除單個通道的陰影"""
        # 膨脹操作
        dilated = cv2.dilate(channel, kernel, iterations=1)
        
        # 計算背景
        bg = cv2.medianBlur(dilated, 21)
        
        # 去除背景
        corrected = 255 - cv2.absdiff(channel, bg)
        
        # 正規化
        normalized = cv2.normalize(corrected, None, 0, 255, cv2.NORM_MINMAX)
        
        return normalized
    
    def auto_crop_plate(self, image, detection_result):
        """自動裁剪車牌區域"""
        if len(detection_result) == 0:
            return None
        
        # 獲取檢測框
        x1, y1, x2, y2 = detection_result[0]['bbox']
        
        # 添加邊距
        margin = 10
        h, w = image.shape[:2]
        
        x1 = max(0, x1 - margin)
        y1 = max(0, y1 - margin)
        x2 = min(w, x2 + margin)
        y2 = min(h, y2 + margin)
        
        # 裁剪車牌
        plate_crop = image[y1:y2, x1:x2]
        
        return plate_crop
    
    def validate_plate_size(self, plate_image, min_width=100, min_height=30):
        """驗證車牌尺寸"""
        h, w = plate_image.shape[:2]
        
        if w < min_width or h < min_height:
            return False
        
        # 檢查寬高比
        aspect_ratio = w / h
        if aspect_ratio < 2.0 or aspect_ratio > 5.0:
            return False
        
        return True