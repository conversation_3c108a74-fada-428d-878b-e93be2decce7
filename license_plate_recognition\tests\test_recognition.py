#!/usr/bin/env python3
"""
車牌辨識系統測試腳本

這個測試腳本用於驗證車牌辨識系統的各個功能模組是否正常工作。
"""

import unittest
import cv2
import numpy as np
import os
import sys
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.preprocessor import ImagePreprocessor
from core.detector import LicensePlateDetector
from core.segmenter import CharacterSegmenter
from core.recognizer import CharacterRecognizer
from core.license_plate_recognition import LicensePlateRecognition
from utils.image_utils import load_image, resize_image, enhance_image
from utils.config import Config

class TestImageUtils(unittest.TestCase):
    """測試圖像工具函數"""
    
    def setUp(self):
        """測試前的準備工作"""
        # 創建一個測試圖像
        self.test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        cv2.rectangle(self.test_image, (10, 10), (90, 90), (255, 255, 255), -1)
        
        # 創建臨時測試目錄
        self.test_dir = Path("test_temp")
        self.test_dir.mkdir(exist_ok=True)
        
        # 保存測試圖像
        self.test_image_path = self.test_dir / "test_image.jpg"
        cv2.imwrite(str(self.test_image_path), self.test_image)
    
    def tearDown(self):
        """測試後的清理工作"""
        # 刪除臨時文件
        if self.test_image_path.exists():
            self.test_image_path.unlink()
        if self.test_dir.exists():
            self.test_dir.rmdir()
    
    def test_load_image(self):
        """測試圖像載入功能"""
        # 測試正常載入
        image = load_image(str(self.test_image_path))
        self.assertIsNotNone(image)
        self.assertEqual(image.shape, (100, 100, 3))
        
        # 測試載入不存在的文件
        with self.assertRaises(FileNotFoundError):
            load_image("non_existent_file.jpg")
    
    def test_resize_image(self):
        """測試圖像調整大小功能"""
        # 測試調整到指定大小
        resized = resize_image(self.test_image, (50, 50), maintain_ratio=False)
        self.assertEqual(resized.shape[:2], (50, 50))
        
        # 測試保持比例調整
        resized = resize_image(self.test_image, (200, 200), maintain_ratio=True)
        self.assertLessEqual(resized.shape[0], 200)
        self.assertLessEqual(resized.shape[1], 200)
    
    def test_enhance_image(self):
        """測試圖像增強功能"""
        enhanced = enhance_image(self.test_image)
        self.assertIsNotNone(enhanced)
        self.assertEqual(enhanced.shape, self.test_image.shape)

class TestPreprocessor(unittest.TestCase):
    """測試圖像預處理器"""
    
    def setUp(self):
        """測試前的準備工作"""
        self.preprocessor = ImagePreprocessor()
        
        # 創建測試圖像
        self.test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        cv2.rectangle(self.test_image, (50, 50), (250, 150), (255, 255, 255), -1)
    
    def test_preprocess_for_detection(self):
        """測試檢測預處理"""
        processed = self.preprocessor.preprocess_for_detection(self.test_image)
        self.assertIsNotNone(processed)
        self.assertEqual(len(processed.shape), 3)  # 應該是彩色圖像
    
    def test_preprocess_for_ocr(self):
        """測試OCR預處理"""
        processed = self.preprocessor.preprocess_for_ocr(self.test_image)
        self.assertIsNotNone(processed)
        self.assertEqual(len(processed.shape), 2)  # 應該是灰度圖像
    
    def test_validate_plate_size(self):
        """測試車牌尺寸驗證"""
        # 測試有效尺寸
        valid_plate = np.zeros((50, 200, 3), dtype=np.uint8)
        self.assertTrue(self.preprocessor.validate_plate_size(valid_plate))
        
        # 測試無效尺寸
        invalid_plate = np.zeros((10, 10, 3), dtype=np.uint8)
        self.assertFalse(self.preprocessor.validate_plate_size(invalid_plate))

class TestDetector(unittest.TestCase):
    """測試車牌檢測器"""
    
    def setUp(self):
        """測試前的準備工作"""
        self.detector = LicensePlateDetector()
        
        # 創建一個模擬車牌的測試圖像
        self.test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        # 繪製一個矩形模擬車牌
        cv2.rectangle(self.test_image, (100, 150), (500, 250), (255, 255, 255), -1)
        # 添加一些文字特徵
        cv2.putText(self.test_image, "ABC123", (150, 200), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
    
    def test_detect(self):
        """測試檢測功能"""
        detections = self.detector.detect(self.test_image)
        
        # 檢測結果應該是列表
        self.assertIsInstance(detections, list)
        
        # 如果有檢測結果，檢查格式
        if detections:
            detection = detections[0]
            self.assertIn('bbox', detection)
            self.assertIn('confidence', detection)
            self.assertIn('class', detection)
            self.assertIn('area', detection)
            
            # 檢查邊界框格式
            bbox = detection['bbox']
            self.assertEqual(len(bbox), 4)
            self.assertIsInstance(bbox[0], int)
    
    def test_get_detection_statistics(self):
        """測試檢測統計功能"""
        # 測試空結果
        stats = self.detector.get_detection_statistics([])
        self.assertEqual(stats['total_detections'], 0)
        self.assertEqual(stats['average_confidence'], 0)
        
        # 測試有結果的情況
        mock_detections = [
            {'confidence': 0.8, 'area': 1000},
            {'confidence': 0.9, 'area': 1200}
        ]
        stats = self.detector.get_detection_statistics(mock_detections)
        self.assertEqual(stats['total_detections'], 2)
        self.assertAlmostEqual(stats['average_confidence'], 0.85)

class TestSegmenter(unittest.TestCase):
    """測試字符分割器"""
    
    def setUp(self):
        """測試前的準備工作"""
        self.segmenter = CharacterSegmenter()
        
        # 創建模擬車牌圖像
        self.plate_image = np.zeros((80, 240), dtype=np.uint8)
        # 繪製幾個模擬字符
        for i in range(6):
            x_start = i * 40 + 5
            x_end = (i + 1) * 40 - 5
            cv2.rectangle(self.plate_image, (x_start, 10), (x_end, 70), 255, -1)
    
    def test_segment_characters(self):
        """測試字符分割功能"""
        characters = self.segmenter.segment_characters(self.plate_image)
        
        # 應該分割出字符
        self.assertIsInstance(characters, list)
        
        # 如果有字符，檢查每個字符
        for char in characters:
            self.assertIsInstance(char, np.ndarray)
            self.assertEqual(len(char.shape), 2)  # 應該是二值圖像
    
    def test_normalize_characters(self):
        """測試字符標準化功能"""
        # 創建一些測試字符
        test_chars = [
            np.zeros((20, 15), dtype=np.uint8),
            np.zeros((25, 20), dtype=np.uint8)
        ]
        
        normalized = self.segmenter.normalize_characters(test_chars, (32, 32))
        
        self.assertEqual(len(normalized), len(test_chars))
        for char in normalized:
            self.assertEqual(char.shape, (32, 32))
    
    def test_validate_segments(self):
        """測試分割結果驗證"""
        # 測試有效字符列表
        valid_chars = [np.zeros((30, 20)) for _ in range(6)]
        self.assertTrue(self.segmenter._validate_segments(valid_chars))
        
        # 測試字符數量不足
        invalid_chars = [np.zeros((30, 20)) for _ in range(2)]
        self.assertFalse(self.segmenter._validate_segments(invalid_chars))

class TestRecognizer(unittest.TestCase):
    """測試字符識別器"""
    
    def setUp(self):
        """測試前的準備工作"""
        self.recognizer = CharacterRecognizer()
        
        # 創建模擬字符圖像
        self.test_char = np.zeros((32, 32), dtype=np.uint8)
        cv2.rectangle(self.test_char, (8, 8), (24, 24), 255, -1)
    
    def test_recognize_characters(self):
        """測試字符識別功能"""
        characters = [self.test_char]
        results = self.recognizer.recognize_characters(characters)
        
        self.assertIsInstance(results, list)
        if results:
            result = results[0]
            self.assertIn('character', result)
            self.assertIn('confidence', result)
            self.assertIn('position', result)
    
    def test_combine_characters(self):
        """測試字符組合功能"""
        mock_results = [
            {'character': 'A', 'position': 0},
            {'character': 'B', 'position': 1},
            {'character': 'C', 'position': 2}
        ]
        
        combined = self.recognizer.combine_characters(mock_results)
        self.assertEqual(combined, "ABC")
    
    def test_validate_plate_format(self):
        """測試車牌格式驗證"""
        # 測試有效格式
        valid_plates = ["AB-1234", "ABC-123", "A12-BC", "1234-AB", "123-ABC"]
        for plate in valid_plates:
            self.assertTrue(self.recognizer.validate_plate_format(plate))
        
        # 測試無效格式
        invalid_plates = ["ABC123", "A-B-C-1-2-3", ""]
        for plate in invalid_plates:
            self.assertFalse(self.recognizer.validate_plate_format(plate))

class TestIntegration(unittest.TestCase):
    """集成測試"""
    
    def setUp(self):
        """測試前的準備工作"""
        self.lpr = LicensePlateRecognition(save_results=False)
        
        # 創建測試圖像
        self.test_image = np.zeros((400, 600, 3), dtype=np.uint8)
        # 繪製模擬車牌
        cv2.rectangle(self.test_image, (150, 180), (450, 220), (255, 255, 255), -1)
        cv2.putText(self.test_image, "ABC-123", (200, 205), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # 保存測試圖像
        self.test_dir = Path("test_temp")
        self.test_dir.mkdir(exist_ok=True)
        self.test_image_path = self.test_dir / "test_plate.jpg"
        cv2.imwrite(str(self.test_image_path), self.test_image)
    
    def tearDown(self):
        """測試後的清理工作"""
        if self.test_image_path.exists():
            self.test_image_path.unlink()
        if self.test_dir.exists():
            self.test_dir.rmdir()
    
    def test_full_recognition_pipeline(self):
        """測試完整的識別流程"""
        result = self.lpr.recognize_from_image(str(self.test_image_path))
        
        # 檢查結果格式
        self.assertIsInstance(result, dict)
        self.assertIn('success', result)
        self.assertIn('plate_text', result)
        self.assertIn('confidence', result)
        self.assertIn('processing_time', result)
        
        # 如果有成功的結果，檢查內容
        if result['success']:
            self.assertIsInstance(result['plate_text'], str)
            self.assertGreater(len(result['plate_text']), 0)
            self.assertGreaterEqual(result['confidence'], 0)
            self.assertLessEqual(result['confidence'], 1)
    
    def test_batch_processing(self):
        """測試批量處理"""
        # 創建多個測試圖像
        image_paths = []
        for i in range(3):
            image_path = self.test_dir / f"test_batch_{i}.jpg"
            cv2.imwrite(str(image_path), self.test_image)
            image_paths.append(str(image_path))
        
        try:
            results = self.lpr.batch_recognize(image_paths)
            
            self.assertEqual(len(results), len(image_paths))
            
            for result in results:
                self.assertIn('success', result)
                self.assertIn('source', result)
                
                if result['success']:
                    self.assertIn('plate_text', result)
                    self.assertIn('confidence', result)
        
        finally:
            # 清理測試文件
            for path in image_paths:
                Path(path).unlink()
    
    def test_statistics(self):
        """測試統計功能"""
        # 執行一次識別
        self.lpr.recognize_from_image(str(self.test_image_path))
        
        # 獲取統計信息
        stats = self.lpr.get_statistics()
        
        self.assertIn('total_images', stats)
        self.assertIn('successful_detections', stats)
        self.assertIn('successful_recognitions', stats)
        self.assertIn('average_processing_time', stats)
        self.assertIn('detection_rate', stats)
        self.assertIn('recognition_rate', stats)
        
        self.assertGreaterEqual(stats['total_images'], 1)
        self.assertGreaterEqual(stats['detection_rate'], 0)
        self.assertLessEqual(stats['detection_rate'], 1)

def create_test_images():
    """創建測試圖像用於演示"""
    test_dir = Path("test_samples")
    test_dir.mkdir(exist_ok=True)
    
    # 創建不同類型的測試圖像
    test_cases = [
        ("simple_plate", "ABC-123"),
        ("complex_plate", "XYZ-4567"),
        ("numbers_only", "123-456"),
        ("letters_only", "ABC-DEF")
    ]
    
    for name, text in test_cases:
        # 創建圖像
        image = np.zeros((300, 500, 3), dtype=np.uint8)
        
        # 繪製車牌背景
        cv2.rectangle(image, (100, 120), (400, 180), (255, 255, 255), -1)
        
        # 添加文字
        cv2.putText(image, text, (150, 155), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 3)
        
        # 保存圖像
        image_path = test_dir / f"{name}.jpg"
        cv2.imwrite(str(image_path), image)
        print(f"創建測試圖像: {image_path}")
    
    print(f"測試圖像已保存到 {test_dir} 目錄")
    return test_dir

def run_performance_test():
    """運行性能測試"""
    print("運行性能測試...")
    
    # 創建測試圖像
    test_dir = create_test_images()
    
    # 初始化識別器
    lpr = LicensePlateRecognition(save_results=False)
    
    # 獲取所有測試圖像
    image_paths = list(test_dir.glob("*.jpg"))
    
    if not image_paths:
        print("沒有找到測試圖像")
        return
    
    print(f"找到 {len(image_paths)} 個測試圖像")
    
    # 測試單張圖像處理
    print("\n測試單張圖像處理:")
    for image_path in image_paths:
        result = lpr.recognize_from_image(str(image_path))
        print(f"  {image_path.name}: {result['plate_text']} "
              f"(置信度: {result['confidence']:.2%}, "
              f"時間: {result['processing_time']:.3f}s)")
    
    # 測試批量處理
    print("\n測試批量處理:")
    image_path_strings = [str(path) for path in image_paths]
    results = lpr.batch_recognize(image_path_strings)
    
    # 顯示統計信息
    stats = lpr.get_statistics()
    print(f"\n統計信息:")
    print(f"  總圖像數: {stats['total_images']}")
    print(f"  成功檢測: {stats['successful_detections']} ({stats['detection_rate']:.1%})")
    print(f"  成功識別: {stats['successful_recognitions']} ({stats['recognition_rate']:.1%})")
    print(f"  平均處理時間: {stats['average_processing_time']:.3f}秒")

def main():
    """主測試函數"""
    print("車牌辨識系統測試腳本")
    print("=" * 50)
    
    # 運行單元測試
    print("運行單元測試...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 50)
    
    # 運行性能測試
    run_performance_test()
    
    print("\n測試完成!")

if __name__ == "__main__":
    main()
