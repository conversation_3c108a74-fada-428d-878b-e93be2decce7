# 車牌辨識系統 (License Plate Recognition System)

這是一個使用 Python 開發的完整車牌辨識系統，支持多種輸入方式和識別模式。

## 功能特性

### 🎯 核心功能
- **多種輸入支持**: 單張圖像、批量圖像、實時攝像頭、文件夾監控
- **智能檢測**: 使用 YOLOv8 進行車牌定位檢測
- **字符分割**: 多種分割算法（垂直投影、連通域、輪廓分析）
- **字符識別**: 支持 PaddleOCR 和模板匹配雙重識別
- **格式驗證**: 自動驗證台灣車牌格式

### 🖥️ 用戶界面
- **命令行界面**: 完整的 CLI 工具，支持各種操作模式
- **圖形界面**: 基於 Tkinter 的桌面應用程序
- **網頁界面**: 基於 Flask 的 Web 應用程序

### 📊 處理能力
- **批量處理**: 支持大量圖像的自動化處理
- **實時處理**: 攝像頭實時識別功能
- **結果導出**: 支持多種格式（TXT、JSON、CSV）
- **統計分析**: 詳細的處理統計和性能分析

## 系統架構

```
license_plate_recognition/
├── core/                    # 核心處理模組
│   ├── preprocessor.py     # 圖像預處理
│   ├── detector.py         # 車牌檢測
│   ├── segmenter.py        # 字符分割
│   ├── recognizer.py       # 字符識別
│   └── license_plate_recognition.py  # 主識別類
├── models/                 # 模型文件
├── utils/                  # 工具函數
│   ├── config.py          # 配置管理
│   └── image_utils.py     # 圖像處理工具
├── interface/              # 用戶界面
│   ├── gui_app.py         # 桌面應用
│   └── web_app.py         # 網頁應用
├── data/                   # 數據存儲
│   ├── samples/           # 樣本圖像
│   ├── results/           # 識別結果
│   └── uploads/           # 上傳文件
├── tests/                  # 測試文件
└── main.py                # 主程序入口
```

## 快速開始

### 1. 安裝依賴

```bash
# 克隆項目或下載代碼
cd license_plate_recognition

# 安裝依賴包
pip install -r requirements.txt
```

### 2. 基本使用

#### 命令行方式

```bash
# 識別單張圖像
python main.py -i image.jpg

# 批量識別
python main.py -b image1.jpg image2.jpg image3.jpg

# 處理整個文件夾
python main.py -f ./images/

# 使用攝像頭實時識別
python main.py -c
```

#### Python API 方式

```python
from core.license_plate_recognition import LicensePlateRecognition

# 初始化識別器
lpr = LicensePlateRecognition()

# 識別單張圖像
result = lpr.recognize_from_image('path/to/image.jpg')
print(f"車牌號碼: {result['plate_text']}")
print(f"置信度: {result['confidence']:.2%}")

# 批量識別
image_paths = ['image1.jpg', 'image2.jpg', 'image3.jpg']
results = lpr.batch_recognize(image_paths)
```

### 3. 圖形界面

```bash
# 啟動桌面應用
python interface/gui_app.py
```

### 4. 網頁界面

```bash
# 啟動網頁服務器
python interface/web_app.py

# 訪問 http://localhost:5000
```

## 技術細節

### 圖像預處理
- **尺寸調整**: 智能縮放保持比例
- **噪聲去除**: 高斯模糊和形態學操作
- **對比度增強**: 自適應直方圖均衡化
- **陰影去除**: 基於形態學的陰影校正

### 車牌檢測
- **主要方法**: YOLOv8 深度學習模型
- **備用方法**: 傳統圖像處理（邊緣檢測 + 輪廓分析）
- **後處理**: 非極大值抑制（NMS）去除重複檢測

### 字符分割
- **垂直投影法**: 基於像素投影的字符邊界檢測
- **連通域分析**: 基於連通組件的字符提取
- **輪廓檢測**: 基於輪廓的字符定位
- **智能選擇**: 自動選擇最佳分割結果

### 字符識別
- **主要方法**: PaddleOCR 深度學習引擎
- **備用方法**: 模板匹配 + 特徵提取
- **字符集**: 支持英文字母、數字、中文
- **格式驗證**: 台灣車牌格式正則表達式驗證

## 配置選項

系統配置位於 `utils/config.py`：

```python
# 主要配置項
IMAGE_SIZE = (640, 640)              # 圖像處理尺寸
CONFIDENCE_THRESHOLD = 0.5           # 檢測置信度閾值
OCR_CONFIDENCE = 0.6                 # OCR識別置信度
CHAR_WIDTH_MIN = 10                  # 最小字符寬度
CHAR_HEIGHT_MIN = 20                 # 最小字符高度
LICENSE_PLATE_PATTERN = r'^[A-Z]{2,3}-\d{3,4}$'  # 車牌格式
```

## 性能優化

### 處理速度
- **模型優化**: 使用輕量級 YOLOv8n 模型
- **圖像縮放**: 智能尺寸調整減少計算量
- **批處理**: 支持多張圖像的批量處理
- **多線程**: 圖形界面使用後台線程處理

### 識別準確率
- **多重驗證**: 多種分割和識別算法組合
- **格式檢查**: 自動驗證車牌格式合法性
- **置信度過濾**: 低置信度結果自動過濾
- **上下文校正**: 基於語言模型的結果優化

## 測試與驗證

### 運行測試

```bash
# 運行所有測試
python tests/test_recognition.py

# 運行特定測試類
python -m unittest tests.test_recognition.TestPreprocessor
```

### 創建測試圖像

```python
from tests.test_recognition import create_test_images

# 創建測試圖像
test_dir = create_test_images()
print(f"測試圖像已創建: {test_dir}")
```

## 常見問題

### Q: 識別準確率不高怎麼辦？
A: 
1. 確保圖像質量良好，分辨率適中
2. 檢查車牌是否清晰可見，無遮擋
3. 調整 `CONFIDENCE_THRESHOLD` 參數
4. 考慮使用更高質量的訓練模型

### Q: 處理速度太慢怎麼辦？
A:
1. 降低圖像分辨率
2. 使用更小的模型（如 YOLOv8n）
3. 啟用 GPU 加速（如果可用）
4. 使用批量處理減少初始化開銷

### Q: 支持哪些車牌格式？
A: 主要支持台灣車牌格式：
- AB-1234（兩個字母 + 四個數字）
- ABC-123（三個字母 + 三個數字）
- A12-BC（字母+數字+字母）
- 其他常見組合

## 擴展開發

### 添加新的檢測模型

```python
# 在 detector.py 中添加新的檢測方法
def _new_detection_method(self, image):
    # 實現新的檢測邏輯
    return detections
```

### 添加新的字符識別引擎

```python
# 在 recognizer.py 中添加新的識別方法
def _recognize_with_new_engine(self, character_images):
    # 實現新的識別邏輯
    return results
```

### 自定義車牌格式

```python
# 在 config.py 中修改格式正則表達式
LICENSE_PLATE_PATTERN = r'your_custom_pattern'
```

## 授權與許可

本項目採用 MIT 許可證，詳見 LICENSE 文件。

## 聯繫與支持

如有問題或建議，請通過以下方式聯繫：
- 提交 Issue
- 發送郵件
- 技術支持

---

**注意**: 這是一個教育演示項目，實際應用中可能需要根據具體需求進行調整和優化。