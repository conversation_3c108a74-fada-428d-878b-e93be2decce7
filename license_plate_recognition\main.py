#!/usr/bin/env python3
"""
車牌辨識系統主程序

這是一個完整的車牌辨識系統，支持：
- 單張圖像識別
- 批量圖像識別
- 實時攝像頭識別
- 文件夾監控識別
"""

import argparse
import sys
import os
from pathlib import Path
from typing import List

# 添加項目根目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.license_plate_recognition import LicensePlateRecognition
from utils.config import Config

def print_banner():
    """打印程序標題"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    車牌辨識系統 v1.0                        ║
║                  License Plate Recognition                   ║
║                                                              ║
║  支持功能：                                                  ║
║  • 單張圖像識別                                             ║
║  • 批量圖像處理                                             ║
║  • 實時攝像頭識別                                           ║
║  • 文件夾批量處理                                           ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def recognize_single_image(image_path: str, save_results: bool = True):
    """識別單張圖像"""
    print(f"正在處理圖像: {image_path}")
    
    # 創建識別器
    lpr = LicensePlateRecognition(save_results=save_results)
    
    # 執行識別
    result = lpr.recognize_from_image(image_path)
    
    # 顯示結果
    if result['success']:
        print(f"✓ 識別成功!")
        print(f"  車牌號碼: {result['plate_text']}")
        print(f"  置信度: {result['confidence']:.2%}")
        print(f"  處理時間: {result['processing_time']:.3f}秒")
    else:
        print(f"✗ 識別失敗: {result.get('error', '未知錯誤')}")
    
    return result

def recognize_batch_images(image_paths: List[str], save_results: bool = True):
    """批量識別圖像"""
    print(f"開始批量處理 {len(image_paths)} 張圖像...")
    
    # 創建識別器
    lpr = LicensePlateRecognition(save_results=save_results)
    
    # 執行批量識別
    results = lpr.batch_recognize(image_paths)
    
    # 顯示統計信息
    stats = lpr.get_statistics()
    print(f"\n批量處理完成!")
    print(f"  總圖像數: {stats['total_images']}")
    print(f"  成功檢測: {stats['successful_detections']} ({stats['detection_rate']:.1%})")
    print(f"  成功識別: {stats['successful_recognitions']} ({stats['recognition_rate']:.1%})")
    print(f"  平均處理時間: {stats['average_processing_time']:.3f}秒")
    
    return results

def recognize_from_camera(camera_id: int = 0, max_frames: int = 30):
    """從攝像頭實時識別"""
    print(f"正在啟動攝像頭 (ID: {camera_id})...")
    print("按 'q' 鍵退出，按 's' 鍵保存當前幀")
    
    # 創建識別器
    lpr = LicensePlateRecognition(save_results=False)
    
    # 執行攝像頭識別
    result = lpr.recognize_from_camera(camera_id, max_frames)
    
    if result['success']:
        print(f"✓ 從攝像頭識別成功!")
        print(f"  車牌號碼: {result['plate_text']}")
        print(f"  置信度: {result['confidence']:.2%}")
    else:
        print(f"✗ 攝像頭識別失敗: {result.get('error', '未知錯誤')}")
    
    return result

def process_folder(folder_path: str, save_results: bool = True):
    """處理文件夾中的所有圖像"""
    folder = Path(folder_path)
    
    # 支持的圖像格式
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    # 查找所有圖像文件
    image_files = []
    for fmt in supported_formats:
        image_files.extend(folder.glob(f"*{fmt}"))
        image_files.extend(folder.glob(f"*{fmt.upper()}"))
    
    if not image_files:
        print(f"在文件夾 {folder_path} 中未找到支持的圖像文件")
        return []
    
    print(f"找到 {len(image_files)} 個圖像文件")
    
    # 轉換為字符串路徑
    image_paths = [str(file) for file in image_files]
    
    # 批量處理
    return recognize_batch_images(image_paths, save_results)

def main():
    """主函數"""
    print_banner()
    
    parser = argparse.ArgumentParser(
        description='車牌辨識系統 - 支持單張圖像、批量處理和攝像頭識別',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 識別單張圖像
  python main.py -i image.jpg
  
  # 批量識別圖像
  python main.py -b image1.jpg image2.jpg image3.jpg
  
  # 處理整個文件夾
  python main.py -f ./images/
  
  # 使用攝像頭實時識別
  python main.py -c
  
  # 指定攝像頭ID
  python main.py -c --camera-id 1
  
  # 不保存結果
  python main.py -i image.jpg --no-save
        """
    )
    
    # 創建互斥組
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('-i', '--image', 
                      help='單張圖像文件路徑')
    group.add_argument('-b', '--batch', 
                      nargs='+', 
                      help='多張圖像文件路徑')
    group.add_argument('-f', '--folder', 
                      help='包含圖像的文件夾路徑')
    group.add_argument('-c', '--camera', 
                      action='store_true', 
                      help='使用攝像頭實時識別')
    
    # 其他選項
    parser.add_argument('--camera-id', 
                       type=int, 
                       default=0, 
                       help='攝像頭ID (默認: 0)')
    parser.add_argument('--no-save', 
                       action='store_true', 
                       help='不保存識別結果')
    parser.add_argument('--version', 
                       action='version', 
                       version='車牌辨識系統 v1.0')
    
    args = parser.parse_args()
    
    try:
        if args.image:
            # 單張圖像識別
            if not Path(args.image).exists():
                print(f"錯誤: 文件 {args.image} 不存在")
                return 1
            
            result = recognize_single_image(args.image, not args.no_save)
            return 0 if result['success'] else 1
            
        elif args.batch:
            # 批量圖像識別
            for image_path in args.batch:
                if not Path(image_path).exists():
                    print(f"錯誤: 文件 {image_path} 不存在")
                    return 1
            
            results = recognize_batch_images(args.batch, not args.no_save)
            success_count = sum(1 for r in results if r['success'])
            return 0 if success_count > 0 else 1
            
        elif args.folder:
            # 文件夾處理
            folder_path = Path(args.folder)
            if not folder_path.exists():
                print(f"錯誤: 文件夾 {args.folder} 不存在")
                return 1
            
            if not folder_path.is_dir():
                print(f"錯誤: {args.folder} 不是文件夾")
                return 1
            
            results = process_folder(args.folder, not args.no_save)
            success_count = sum(1 for r in results if r['success'])
            return 0 if success_count > 0 else 1
            
        elif args.camera:
            # 攝像頭識別
            result = recognize_from_camera(args.camera_id)
            return 0 if result['success'] else 1
            
    except KeyboardInterrupt:
        print("\n操作被用戶中斷")
        return 0
    except Exception as e:
        print(f"錯誤: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())