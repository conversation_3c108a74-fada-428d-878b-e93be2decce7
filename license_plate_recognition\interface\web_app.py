from flask import Flask, render_template, request, jsonify, send_file
import os
import cv2
import numpy as np
from pathlib import Path
from werkzeug.utils import secure_filename
import json
import uuid
from datetime import datetime

# 添加項目根目錄到Python路徑
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.license_plate_recognition import LicensePlateRecognition
from utils.config import Config

app = Flask(__name__)
app.config['SECRET_KEY'] = 'license-plate-recognition-secret-key'
app.config['UPLOAD_FOLDER'] = Config.DATA_DIR / 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 確保上傳目錄存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 支持的文件格式
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'tiff', 'tif'}

# 初始化識別器
lpr = LicensePlateRecognition(save_results=True)

def allowed_file(filename):
    """檢查文件格式是否允許"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_unique_filename(original_filename):
    """生成唯一的文件名"""
    ext = original_filename.rsplit('.', 1)[1].lower()
    unique_name = f"{uuid.uuid4().hex}.{ext}"
    return unique_name

@app.route('/')
def index():
    """主頁面"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """處理文件上傳"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '沒有上傳文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '沒有選擇文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式'}), 400
        
        # 生成唯一文件名
        filename = generate_unique_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        
        # 保存文件
        file.save(filepath)
        
        # 執行識別
        result = lpr.recognize_from_image(filepath)
        
        # 添加文件信息
        result['filename'] = filename
        result['original_filename'] = secure_filename(file.filename)
        result['upload_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'處理過程中出錯: {str(e)}'}), 500

@app.route('/batch_upload', methods=['POST'])
def batch_upload():
    """批量文件上傳"""
    try:
        if 'files' not in request.files:
            return jsonify({'error': '沒有上傳文件'}), 400
        
        files = request.files.getlist('files')
        if not files or files[0].filename == '':
            return jsonify({'error': '沒有選擇文件'}), 400
        
        # 驗證所有文件
        for file in files:
            if not allowed_file(file.filename):
                return jsonify({'error': f'文件 {file.filename} 格式不支持'}), 400
        
        # 保存所有文件
        file_paths = []
        for file in files:
            filename = generate_unique_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            file_paths.append(filepath)
        
        # 執行批量識別
        results = lpr.batch_recognize(file_paths)
        
        # 添加文件信息
        for i, result in enumerate(results):
            result['filename'] = os.path.basename(file_paths[i])
            result['original_filename'] = secure_filename(files[i].filename)
            result['upload_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 獲取統計信息
        stats = lpr.get_statistics()
        
        return jsonify({
            'results': results,
            'statistics': stats
        })
        
    except Exception as e:
        return jsonify({'error': f'批量處理過程中出錯: {str(e)}'}), 500

@app.route('/camera_capture', methods=['POST'])
def camera_capture():
    """攝像頭捕獲識別"""
    try:
        # 獲取攝像頭ID
        camera_id = int(request.json.get('camera_id', 0))
        max_frames = int(request.json.get('max_frames', 10))
        
        # 執行攝像頭識別
        result = lpr.recognize_from_camera(camera_id, max_frames)
        
        # 添加捕獲信息
        result['camera_id'] = camera_id
        result['capture_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'攝像頭處理過程中出錯: {str(e)}'}), 500

@app.route('/result/<filename>')
def get_result_image(filename):
    """獲取結果圖像"""
    try:
        # 構建可能的圖像路徑
        possible_paths = [
            Config.RESULTS_DIR / f"{filename}_detected.jpg",
            Config.RESULTS_DIR / f"{filename}_plate.jpg",
            Config.RESULTS_DIR / f"{filename}_processed.jpg",
            Config.DATA_DIR / 'uploads' / filename
        ]
        
        for path in possible_paths:
            if path.exists():
                return send_file(str(path))
        
        return jsonify({'error': '圖像文件不存在'}), 404
        
    except Exception as e:
        return jsonify({'error': f'獲取圖像失敗: {str(e)}'}), 500

@app.route('/character/<filename>/<int:index>')
def get_character_image(filename, index):
    """獲取字符圖像"""
    try:
        char_path = Config.RESULTS_DIR / f"{filename}_char_{index}.jpg"
        if char_path.exists():
            return send_file(str(char_path))
        else:
            return jsonify({'error': '字符圖像不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'獲取字符圖像失敗: {str(e)}'}), 500

@app.route('/statistics')
def get_statistics():
    """獲取統計信息"""
    try:
        stats = lpr.get_statistics()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': f'獲取統計信息失敗: {str(e)}'}), 500

@app.route('/history')
def get_history():
    """獲取歷史記錄"""
    try:
        # 從結果目錄讀取歷史記錄
        history = []
        result_files = list(Config.RESULTS_DIR.glob("*_result.json"))
        
        for result_file in result_files:
            try:
                with open(result_file, 'r', encoding='utf-8') as f:
                    result = json.load(f)
                    history.append({
                        'filename': result_file.stem.replace('_result', ''),
                        'plate_text': result.get('plate_text', ''),
                        'confidence': result.get('confidence', 0),
                        'success': result.get('success', False),
                        'processing_time': result.get('processing_time', 0),
                        'timestamp': result.get('upload_time', '')
                    })
            except Exception as e:
                print(f"讀取歷史文件失敗 {result_file}: {e}")
                continue
        
        # 按時間排序
        history.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        return jsonify({'history': history[:50]})  # 只返回最近50條
        
    except Exception as e:
        return jsonify({'error': f'獲取歷史記錄失敗: {str(e)}'}), 500

@app.route('/clear_history', methods=['POST'])
def clear_history():
    """清除歷史記錄"""
    try:
        # 刪除所有結果文件
        result_files = list(Config.RESULTS_DIR.glob("*_result.*"))
        for file in result_files:
            try:
                file.unlink()
            except Exception as e:
                print(f"刪除文件失敗 {file}: {e}")
        
        # 重置統計信息
        lpr.reset_statistics()
        
        return jsonify({'success': True, 'message': '歷史記錄已清除'})
        
    except Exception as e:
        return jsonify({'error': f'清除歷史記錄失敗: {str(e)}'}), 500

# 創建templates目錄和HTML模板
templates_dir = Path(__file__).parent / 'templates'
os.makedirs(templates_dir, exist_ok=True)

# HTML模板
html_template = '''<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>車牌辨識系統</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .upload-section, .result-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 1.3em;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }
        
        .upload-icon {
            font-size: 3em;
            color: #ccc;
            margin-bottom: 10px;
        }
        
        .upload-text {
            font-size: 1.1em;
            color: #666;
            margin-bottom: 15px;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result-display {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }
        
        .plate-text {
            font-size: 2em;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 2px solid #667eea;
        }
        
        .confidence {
            text-align: center;
            font-size: 1.1em;
            color: #666;
            margin-bottom: 10px;
        }
        
        .processing-time {
            text-align: center;
            font-size: 0.9em;
            color: #888;
        }
        
        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 15px;
        }
        
        .history-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .history-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-plate {
            font-weight: bold;
            font-size: 1.1em;
            color: #333;
        }
        
        .history-confidence {
            color: #666;
            font-size: 0.9em;
        }
        
        .history-time {
            color: #888;
            font-size: 0.8em;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>車牌辨識系統</h1>
            <p>智能圖像識別技術，準確識別車牌號碼</p>
        </div>
        
        <div class="main-content">
            <div class="upload-section">
                <h2 class="section-title">上傳圖像</h2>
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">拖放圖像到這裡或點擊選擇</div>
                    <input type="file" id="fileInput" class="file-input" 
                           accept="image/*" multiple>
                    <button class="btn" onclick="document.getElementById('fileInput').click()">
                        選擇圖像
                    </button>
                </div>
                
                <div style="margin-top: 20px;">
                    <button class="btn" onclick="startCamera()">📹 攝像頭識別</button>
                    <button class="btn" onclick="clearResults()">🗑️ 清除結果</button>
                </div>
            </div>
            
            <div class="result-section">
                <h2 class="section-title">識別結果</h2>
                <div id="resultDisplay">
                    <p style="text-align: center; color: #666; margin-top: 50px;">
                        請上傳圖像開始識別
                    </p>
                </div>
            </div>
        </div>
        
        <div class="stats" id="statistics">
            <div class="stat-card">
                <div class="stat-number" id="totalImages">0</div>
                <div class="stat-label">總處理圖像</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">識別成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgTime">0s</div>
                <div class="stat-label">平均處理時間</div>
            </div>
        </div>
        
        <div class="history-section">
            <h2 class="section-title">歷史記錄</h2>
            <div id="historyList">
                <p style="text-align: center; color: #666;">暫無歷史記錄</p>
            </div>
        </div>
    </div>
    
    <script>
        let currentFile = null;
        
        // 文件上傳處理
        document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        
        // 拖放處理
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        
        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }
        
        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }
        
        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                if (files.length === 1) {
                    processSingleFile(files[0]);
                } else {
                    processBatchFiles(files);
                }
            }
        }
        
        function handleFileSelect(e) {
            const files = e.target.files;
            if (files.length > 0) {
                if (files.length === 1) {
                    processSingleFile(files[0]);
                } else {
                    processBatchFiles(files);
                }
            }
        }
        
        function processSingleFile(file) {
            if (!file.type.startsWith('image/')) {
                showError('請選擇圖像文件');
                return;
            }
            
            currentFile = file;
            displayImage(file);
            recognizeImage(file);
        }
        
        function processBatchFiles(files) {
            const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
            
            if (imageFiles.length === 0) {
                showError('請選擇至少一個圖像文件');
                return;
            }
            
            recognizeBatchImages(imageFiles);
        }
        
        function displayImage(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const resultDisplay = document.getElementById('resultDisplay');
                resultDisplay.innerHTML = `
                    <img src="${e.target.result}" class="image-preview" alt="預覽圖像">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在識別中...</p>
                    </div>
                `;
            };
            reader.readAsDataURL(file);
        }
        
        async function recognizeImage(file) {
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    displayResult(result);
                    updateStatistics();
                    loadHistory();
                } else {
                    showError(result.error || '識別失敗');
                }
            } catch (error) {
                showError('網絡錯誤: ' + error.message);
            }
        }
        
        async function recognizeBatchImages(files) {
            const formData = new FormData();
            files.forEach(file => formData.append('files', file));
            
            const resultDisplay = document.getElementById('resultDisplay');
            resultDisplay.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在批量處理 ${files.length} 個文件...</p>
                </div>
            `;
            
            try {
                const response = await fetch('/batch_upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    displayBatchResults(data);
                    updateStatistics();
                    loadHistory();
                } else {
                    showError(data.error || '批量處理失敗');
                }
            } catch (error) {
                showError('網絡錯誤: ' + error.message);
            }
        }
        
        function displayResult(result) {
            const resultDisplay = document.getElementById('resultDisplay');
            
            if (result.success) {
                resultDisplay.innerHTML = `
                    <div class="success">
                        <div class="plate-text">${result.plate_text}</div>
                        <div class="confidence">置信度: ${(result.confidence * 100).toFixed(1)}%</div>
                        <div class="processing-time">處理時間: ${result.processing_time.toFixed(3)}秒</div>
                        ${result.filename ? `<img src="/result/${result.filename}" class="image-preview" alt="處理結果">` : ''}
                    </div>
                `;
            } else {
                resultDisplay.innerHTML = `
                    <div class="error">
                        <p>識別失敗: ${result.error || '未知錯誤'}</p>
                    </div>
                `;
            }
        }
        
        function displayBatchResults(data) {
            const resultDisplay = document.getElementById('resultDisplay');
            const stats = data.statistics;
            
            let html = `
                <div class="success">
                    <h3>批量處理完成</h3>
                    <p>總圖像數: ${stats.total_images}</p>
                    <p>成功檢測: ${stats.successful_detections} (${(stats.detection_rate * 100).toFixed(1)}%)</p>
                    <p>成功識別: ${stats.successful_recognitions} (${(stats.recognition_rate * 100).toFixed(1)}%)</p>
                    <p>平均處理時間: ${stats.average_processing_time.toFixed(3)}秒</p>
                    <hr style="margin: 15px 0;">
                    <h4>詳細結果:</h4>
            `;
            
            data.results.forEach((result, index) => {
                const fileName = result.original_filename || `圖像 ${index + 1}`;
                if (result.success) {
                    html += `<p>✓ ${fileName}: ${result.plate_text} (${(result.confidence * 100).toFixed(1)}%)</p>`;
                } else {
                    html += `<p>✗ ${fileName}: 失敗 - ${result.error || '未知錯誤'}</p>`;
                }
            });
            
            html += '</div>';
            resultDisplay.innerHTML = html;
        }
        
        function showError(message) {
            const resultDisplay = document.getElementById('resultDisplay');
            resultDisplay.innerHTML = `
                <div class="error">
                    <p>${message}</p>
                </div>
            `;
        }
        
        async function startCamera() {
            try {
                const resultDisplay = document.getElementById('resultDisplay');
                resultDisplay.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在啟動攝像頭...</p>
                    </div>
                `;
                
                const response = await fetch('/camera_capture', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        camera_id: 0,
                        max_frames: 10
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    displayResult(result);
                    updateStatistics();
                    loadHistory();
                } else {
                    showError(result.error || '攝像頭識別失敗');
                }
            } catch (error) {
                showError('網絡錯誤: ' + error.message);
            }
        }
        
        async function updateStatistics() {
            try {
                const response = await fetch('/statistics');
                const stats = await response.json();
                
                document.getElementById('totalImages').textContent = stats.total_images;
                document.getElementById('successRate').textContent = 
                    (stats.recognition_rate * 100).toFixed(1) + '%';
                document.getElementById('avgTime').textContent = 
                    stats.average_processing_time.toFixed(3) + 's';
            } catch (error) {
                console.error('更新統計信息失敗:', error);
            }
        }
        
        async function loadHistory() {
            try {
                const response = await fetch('/history');
                const data = await response.json();
                
                const historyList = document.getElementById('historyList');
                
                if (data.history && data.history.length > 0) {
                    let html = '';
                    data.history.forEach(item => {
                        html += `
                            <div class="history-item">
                                <div>
                                    <div class="history-plate">${item.plate_text || '無法識別'}</div>
                                    <div class="history-confidence">置信度: ${(item.confidence * 100).toFixed(1)}%</div>
                                </div>
                                <div class="history-time">${item.timestamp}</div>
                            </div>
                        `;
                    });
                    historyList.innerHTML = html;
                } else {
                    historyList.innerHTML = '<p style="text-align: center; color: #666;">暫無歷史記錄</p>';
                }
            } catch (error) {
                console.error('加載歷史記錄失敗:', error);
            }
        }
        
        function clearResults() {
            document.getElementById('resultDisplay').innerHTML = `
                <p style="text-align: center; color: #666; margin-top: 50px;">
                    請上傳圖像開始識別
                </p>
            `;
        }
        
        // 頁面加載完成後更新統計信息和歷史記錄
        window.addEventListener('load', function() {
            updateStatistics();
            loadHistory();
        });
    </script>
</body>
</html>'''

# 寫入HTML模板文件
with open(templates_dir / 'index.html', 'w', encoding='utf-8') as f:
    f.write(html_template)

if __name__ == '__main__':
    print("啟動車牌辨識系統網頁服務器...")
    print(f"訪問 http://localhost:5000 使用網頁界面")
    app.run(host='0.0.0.0', port=5000, debug=True)