import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
from PIL import Image, ImageTk
import threading
import os
from pathlib import Path

# 添加項目根目錄到Python路徑
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.license_plate_recognition import LicensePlateRecognition
from utils.config import Config

class LicensePlateRecognitionGUI:
    """車牌識別圖形界面應用"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("車牌辨識系統 - License Plate Recognition")
        self.root.geometry("1000x700")
        
        # 初始化識別器
        self.lpr = None
        self.current_image = None
        self.current_result = None
        
        # 創建界面
        self.setup_ui()
        
        # 初始化識別器（在後台線程中）
        self.init_recognizer()
    
    def setup_ui(self):
        """設置用戶界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 標題
        title_label = ttk.Label(main_frame, text="車牌辨識系統", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=10)
        
        # 控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 按鈕
        ttk.Button(control_frame, text="選擇圖像", 
                  command=self.select_image).grid(row=0, column=0, padx=5)
        ttk.Button(control_frame, text="開始識別", 
                  command=self.recognize_image).grid(row=0, column=1, padx=5)
        ttk.Button(control_frame, text="保存結果", 
                  command=self.save_result).grid(row=0, column=2, padx=5)
        ttk.Button(control_frame, text="清除結果", 
                  command=self.clear_result).grid(row=0, column=3, padx=5)
        ttk.Button(control_frame, text="批量處理", 
                  command=self.batch_process).grid(row=0, column=4, padx=5)
        
        # 選項
        self.save_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="自動保存結果", 
                       variable=self.save_var).grid(row=0, column=5, padx=20)
        
        # 圖像顯示區域
        image_frame = ttk.LabelFrame(main_frame, text="圖像顯示", padding="10")
        image_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5)
        
        # 原始圖像
        self.original_label = ttk.Label(image_frame, text="原始圖像")
        self.original_label.grid(row=0, column=0, padx=5, pady=5)
        
        # 處理後圖像
        self.processed_label = ttk.Label(image_frame, text="處理後圖像")
        self.processed_label.grid(row=0, column=1, padx=5, pady=5)
        
        # 結果顯示區域
        result_frame = ttk.LabelFrame(main_frame, text="識別結果", padding="10")
        result_frame.grid(row=2, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5)
        
        # 結果文本
        self.result_text = tk.Text(result_frame, width=30, height=15, wrap=tk.WORD)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滾動條
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, 
                                 command=self.result_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.result_text.config(yscrollcommand=scrollbar.set)
        
        # 狀態欄
        self.status_var = tk.StringVar(value="就緒")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var,
                                           maximum=100)
        self.progress_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
    
    def init_recognizer(self):
        """初始化識別器"""
        def init():
            try:
                self.update_status("正在初始化識別器...")
                self.lpr = LicensePlateRecognition(save_results=False)
                self.update_status("識別器初始化完成")
            except Exception as e:
                self.update_status(f"識別器初始化失敗: {e}")
                messagebox.showerror("錯誤", f"識別器初始化失敗: {e}")
        
        thread = threading.Thread(target=init)
        thread.daemon = True
        thread.start()
    
    def select_image(self):
        """選擇圖像文件"""
        file_path = filedialog.askopenfilename(
            title="選擇圖像文件",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.load_image(file_path)
    
    def load_image(self, image_path):
        """載入並顯示圖像"""
        try:
            # 載入圖像
            image = cv2.imread(image_path)
            if image is None:
                messagebox.showerror("錯誤", "無法載入圖像文件")
                return
            
            self.current_image = image
            self.current_image_path = image_path
            
            # 轉換為RGB格式
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 調整大小以適應顯示區域
            display_width = 400
            display_height = 300
            image_resized = self.resize_image_for_display(image_rgb, display_width, display_height)
            
            # 轉換為PhotoImage
            photo = ImageTk.PhotoImage(image=Image.fromarray(image_resized))
            
            # 顯示圖像
            self.original_label.config(image=photo)
            self.original_label.image = photo
            
            self.update_status(f"已載入圖像: {Path(image_path).name}")
            
        except Exception as e:
            messagebox.showerror("錯誤", f"載入圖像失敗: {e}")
    
    def resize_image_for_display(self, image, max_width, max_height):
        """調整圖像大小以適應顯示區域"""
        height, width = image.shape[:2]
        
        # 計算縮放比例
        scale = min(max_width / width, max_height / height)
        
        if scale < 1:
            new_width = int(width * scale)
            new_height = int(height * scale)
            return cv2.resize(image, (new_width, new_height))
        else:
            return image
    
    def recognize_image(self):
        """識別當前圖像"""
        if self.current_image is None:
            messagebox.showwarning("警告", "請先選擇圖像")
            return
        
        if self.lpr is None:
            messagebox.showwarning("警告", "識別器尚未準備就緒")
            return
        
        def recognize():
            try:
                self.update_status("正在識別...")
                self.set_progress(0)
                
                # 執行識別
                result = self.lpr.recognize_from_image(
                    self.current_image_path, 
                    save_results=self.save_var.get()
                )
                
                self.current_result = result
                self.set_progress(100)
                
                # 更新界面
                self.root.after(0, self.display_result, result)
                
                if result['success']:
                    self.update_status(f"識別完成: {result['plate_text']}")
                else:
                    self.update_status(f"識別失敗: {result.get('error', '未知錯誤')}")
                    
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("錯誤", f"識別失敗: {e}"))
                self.update_status("識別失敗")
                self.set_progress(0)
        
        # 在後台線程中執行識別
        thread = threading.Thread(target=recognize)
        thread.daemon = True
        thread.start()
    
    def display_result(self, result):
        """顯示識別結果"""
        # 清空結果文本
        self.result_text.delete(1.0, tk.END)
        
        # 顯示結果
        if result['success']:
            self.result_text.insert(tk.END, f"車牌號碼: {result['plate_text']}\n\n")
            self.result_text.insert(tk.END, f"置信度: {result['confidence']:.2%}\n\n")
            self.result_text.insert(tk.END, f"處理時間: {result['processing_time']:.3f}秒\n\n")
            
            # 顯示詳細信息
            self.result_text.insert(tk.END, "詳細信息:\n")
            self.result_text.insert(tk.END, f"  檢測到車牌: {len(result['detections'])}個\n")
            self.result_text.insert(tk.END, f"  分割字符: {len(result['recognition_results'])}個\n")
            self.result_text.insert(tk.END, f"  格式驗證: {'通過' if result.get('is_valid_format', False) else '未通過'}\n")
            
        else:
            self.result_text.insert(tk.END, f"識別失敗\n\n")
            self.result_text.insert(tk.END, f"錯誤: {result.get('error', '未知錯誤')}\n")
        
        # 顯示處理階段
        if 'processing_stages' in result:
            self.result_text.insert(tk.END, "\n處理階段:\n")
            for stage, value in result['processing_stages'].items():
                self.result_text.insert(tk.END, f"  {stage}: {value}\n")
        
        # 如果有處理後的圖像，顯示它
        if result.get('detections'):
            self.display_processed_image(result)
    
    def display_processed_image(self, result):
        """顯示處理後的圖像"""
        try:
            # 在原始圖像上繪製檢測框
            image_with_boxes = self.current_image.copy()
            
            for detection in result['detections']:
                x1, y1, x2, y2 = detection['bbox']
                cv2.rectangle(image_with_boxes, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # 添加置信度標籤
                confidence = detection['confidence']
                label = f"{confidence:.2%}"
                cv2.putText(image_with_boxes, label, (x1, y1-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # 轉換為RGB格式
            image_rgb = cv2.cvtColor(image_with_boxes, cv2.COLOR_BGR2RGB)
            
            # 調整大小
            display_width = 400
            display_height = 300
            image_resized = self.resize_image_for_display(image_rgb, display_width, display_height)
            
            # 轉換為PhotoImage
            photo = ImageTk.PhotoImage(image=Image.fromarray(image_resized))
            
            # 顯示圖像
            self.processed_label.config(image=photo)
            self.processed_label.image = photo
            
        except Exception as e:
            print(f"顯示處理後圖像失敗: {e}")
    
    def save_result(self):
        """保存識別結果"""
        if self.current_result is None:
            messagebox.showwarning("警告", "沒有結果可保存")
            return
        
        try:
            # 選擇保存位置
            file_path = filedialog.asksaveasfilename(
                title="保存識別結果",
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("JSON files", "*.json"),
                    ("All files", "*.*")
                ]
            )
            
            if file_path:
                # 保存結果
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    if file_path.endswith('.json'):
                        json.dump(self.current_result, f, ensure_ascii=False, indent=2)
                    else:
                        f.write(f"車牌號碼: {self.current_result.get('plate_text', '')}\n")
                        f.write(f"置信度: {self.current_result.get('confidence', 0):.2%}\n")
                        f.write(f"成功: {self.current_result.get('success', False)}\n")
                
                self.update_status(f"結果已保存: {Path(file_path).name}")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"保存結果失敗: {e}")
    
    def clear_result(self):
        """清除當前結果"""
        self.current_result = None
        self.result_text.delete(1.0, tk.END)
        self.processed_label.config(image='')
        self.processed_label.image = None
        self.update_status("結果已清除")
    
    def batch_process(self):
        """批量處理"""
        # 選擇多個圖像文件
        file_paths = filedialog.askopenfilenames(
            title="選擇多個圖像文件",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                ("All files", "*.*")
            ]
        )
        
        if not file_paths:
            return
        
        if self.lpr is None:
            messagebox.showwarning("警告", "識別器尚未準備就緒")
            return
        
        def batch_recognize():
            try:
                self.update_status(f"正在批量處理 {len(file_paths)} 個文件...")
                
                # 執行批量識別
                results = self.lpr.batch_recognize(list(file_paths))
                
                # 顯示統計信息
                stats = self.lpr.get_statistics()
                
                self.root.after(0, self.display_batch_results, results, stats)
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("錯誤", f"批量處理失敗: {e}"))
        
        # 在後台線程中執行
        thread = threading.Thread(target=batch_recognize)
        thread.daemon = True
        thread.start()
    
    def display_batch_results(self, results, stats):
        """顯示批量處理結果"""
        # 清空結果文本
        self.result_text.delete(1.0, tk.END)
        
        # 顯示統計信息
        self.result_text.insert(tk.END, "批量處理統計:\n")
        self.result_text.insert(tk.END, f"總圖像數: {stats['total_images']}\n")
        self.result_text.insert(tk.END, f"成功檢測: {stats['successful_detections']} ({stats['detection_rate']:.1%})\n")
        self.result_text.insert(tk.END, f"成功識別: {stats['successful_recognitions']} ({stats['recognition_rate']:.1%})\n")
        self.result_text.insert(tk.END, f"平均處理時間: {stats['average_processing_time']:.3f}秒\n\n")
        
        # 顯示每個文件的結果
        self.result_text.insert(tk.END, "詳細結果:\n")
        for i, result in enumerate(results):
            file_name = Path(result['source']).name
            if result['success']:
                self.result_text.insert(tk.END, f"{i+1}. {file_name}: {result['plate_text']} ({result['confidence']:.1%})\n")
            else:
                self.result_text.insert(tk.END, f"{i+1}. {file_name}: 失敗 - {result.get('error', '未知錯誤')}\n")
        
        self.update_status(f"批量處理完成: {stats['successful_recognitions']}/{stats['total_images']} 成功")
    
    def update_status(self, message):
        """更新狀態欄"""
        self.status_var.set(message)
    
    def set_progress(self, value):
        """設置進度條"""
        self.progress_var.set(value)
    
    def run(self):
        """運行應用"""
        self.root.mainloop()

if __name__ == "__main__":
    app = LicensePlateRecognitionGUI()
    app.run()