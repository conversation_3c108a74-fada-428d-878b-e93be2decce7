import cv2
import numpy as np
from pathlib import Path

# 創建更真實的測試圖像
test_dir = Path("test_samples")
test_dir.mkdir(exist_ok=True)

print("創建更真實的測試圖像...")

# 創建一個模擬真實場景的圖像
image = np.ones((600, 800, 3), dtype=np.uint8) * 50  # 灰色背景

# 繪製汽車輪廓
cv2.rectangle(image, (100, 200), (700, 500), (100, 100, 150), -1)  # 車身
cv2.rectangle(image, (150, 150), (650, 250), (120, 120, 170), -1)  # 車頂

# 繪製車輪
cv2.circle(image, (200, 520), 40, (30, 30, 30), -1)
cv2.circle(image, (600, 520), 40, (30, 30, 30), -1)

# 繪製車牌區域（更真實的車牌）
plate_x, plate_y = 300, 400
plate_width, plate_height = 200, 60

# 車牌背景
cv2.rectangle(image, (plate_x, plate_y), (plate_x + plate_width, plate_y + plate_height), (255, 255, 255), -1)

# 車牌邊框
cv2.rectangle(image, (plate_x, plate_y), (plate_x + plate_width, plate_y + plate_height), (0, 0, 0), 3)

# 添加車牌文字（台灣車牌格式）
plate_text = "ABC-123"
font = cv2.FONT_HERSHEY_SIMPLEX
font_scale = 1.2
font_thickness = 3
text_size = cv2.getTextSize(plate_text, font, font_scale, font_thickness)[0]
text_x = plate_x + (plate_width - text_size[0]) // 2
text_y = plate_y + (plate_height + text_size[1]) // 2

cv2.putText(image, plate_text, (text_x, text_y), font, font_scale, (0, 0, 0), font_thickness)

# 保存圖像
image_path = test_dir / "realistic_car.jpg"
cv2.imwrite(str(image_path), image)
print(f"創建: {image_path}")

# 創建另一個角度的車牌
image2 = np.ones((500, 700, 3), dtype=np.uint8) * 80  # 淺灰色背景

# 透視變換的車牌（模擬角度）
pts1 = np.float32([[50, 50], [250, 30], [280, 120], [30, 140]])
pts2 = np.float32([[0, 0], [300, 0], [300, 100], [0, 100]])

M = cv2.getPerspectiveTransform(pts1, pts2)
plate_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
cv2.putText(plate_image, "XYZ-4567", (50, 65), font, 1.5, (0, 0, 0), 3)

transformed_plate = cv2.warpPerspective(plate_image, M, (700, 500))
mask = transformed_plate > 200
image2[mask] = transformed_plate[mask]

cv2.putText(image2, "XYZ-4567", (350, 250), font, 1.2, (0, 0, 0), 3)

image_path2 = test_dir / "perspective_plate.jpg"
cv2.imwrite(str(image_path2), image2)
print(f"創建: {image_path2}")

# 創建簡單的車牌特寫
plate_closeup = np.ones((120, 360, 3), dtype=np.uint8) * 255
cv2.rectangle(plate_closeup, (5, 5), (355, 115), (0, 0, 0), 2)
cv2.putText(plate_closeup, "AB-1234", (40, 80), font, 1.8, (0, 0, 0), 3)

image_path3 = test_dir / "plate_closeup.jpg"
cv2.imwrite(str(image_path3), plate_closeup)
print(f"創建: {image_path3}")

print("\n創建測試腳本...")

# 創建簡化測試腳本
simple_test_script = """
# 簡化測試腳本
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.license_plate_recognition import LicensePlateRecognition

print("初始化識別器...")
lpr = LicensePlateRecognition(save_results=False)  # 不保存結果以提高速度

# 測試圖像
test_images = [
    "test_samples/plate_closeup.jpg",
    "test_samples/realistic_car.jpg", 
    "test_samples/perspective_plate.jpg"
]

print("開始識別測試...")
for image_path in test_images:
    if os.path.exists(image_path):
        print(f"\n處理: {image_path}")
        try:
            result = lpr.recognize_from_image(image_path)
            
            if result['success']:
                print(f"  識別成功!")
                print(f"  車牌號碼: {result['plate_text']}")
                print(f"  置信度: {result['confidence']:.2%}")
                print(f"  處理時間: {result['processing_time']:.3f}秒")
                
                # 顯示檢測到的車牌數量
                print(f"  檢測到車牌: {len(result['detections'])}個")
                
            else:
                print(f"  識別失敗: {result.get('error', '未知錯誤')}")
                
        except Exception as e:
            print(f"  處理錯誤: {e}")
    else:
        print(f"  文件不存在: {image_path}")

# 顯示統計信息
stats = lpr.get_statistics()
print(f"\n統計信息:")
print(f"  總圖像數: {stats['total_images']}")
print(f"  成功檢測: {stats['successful_detections']} ({stats['detection_rate']:.1%})")
print(f"  成功識別: {stats['successful_recognitions']} ({stats['recognition_rate']:.1%})")
print(f"  平均處理時間: {stats['average_processing_time']:.3f}秒")
"""

with open("simple_test.py", "w", encoding="utf-8") as f:
    f.write(simple_test_script)

print("簡化測試腳本已創建: simple_test.py")
print("\n運行以下命令開始測試:")
print("venv\\Scripts\\python simple_test.py")