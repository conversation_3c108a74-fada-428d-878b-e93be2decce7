# 測試基本導入
print("測試基本導入...")

try:
    import cv2
    print(f"OK OpenCV {cv2.__version__}")
except ImportError as e:
    print(f"ERROR OpenCV: {e}")

try:
    import numpy as np
    print(f"OK NumPy {np.__version__}")
except ImportError as e:
    print(f"ERROR NumPy: {e}")

try:
    from PIL import Image
    print("OK Pillow")
except ImportError as e:
    print(f"ERROR Pillow: {e}")

try:
    import flask
    print(f"OK Flask {flask.__version__}")
except ImportError as e:
    print(f"ERROR Flask: {e}")

try:
    import matplotlib
    print("OK Matplotlib")
except ImportError as e:
    print(f"ERROR Matplotlib: {e}")

try:
    import pandas as pd
    print(f"OK Pandas {pd.__version__}")
except ImportError as e:
    print(f"ERROR Pandas: {e}")

print("\n測試深度學習庫...")

try:
    import torch
    print(f"OK PyTorch {torch.__version__}")
except ImportError as e:
    print(f"ERROR PyTorch: {e}")

try:
    import ultralytics
    print("OK Ultralytics (YOLO)")
except ImportError as e:
    print(f"ERROR Ultralytics: {e}")

try:
    import paddleocr
    print("OK PaddleOCR")
except ImportError as e:
    print(f"ERROR PaddleOCR: {e}")

print("\n基本導入測試完成！")