# 車牌辨識系統安裝指南

## 系統要求

- **操作系統**: Windows 10/11, Linux, macOS
- **Python版本**: Python 3.8 或更高版本
- **內存**: 至少 4GB RAM
- **存儲空間**: 至少 2GB 可用磁盤空間

## 安裝步驟

### 1. 創建虛擬環境

```bash
cd license_plate_recognition
python -m venv venv
```

### 2. 激活虛擬環境

**Windows:**
```bash
venv\Scripts\activate
```

**Linux/macOS:**
```bash
source venv/bin/activate
```

### 3. 升級 pip

```bash
venv\Scripts\python -m pip install --upgrade pip
```

### 4. 安裝依賴包

我們已經成功安裝了核心依賴包：

```bash
# 基本依賴（已安裝）
venv\Scripts\pip install opencv-python>=4.5.0
venv\Scripts\pip install Pillow>=8.0.0
venv\Scripts\pip install numpy>=1.19.0
venv\Scripts\pip install flask>=2.0.0
venv\Scripts\pip install matplotlib>=3.3.0
venv\Scripts\pip install pandas>=1.3.0
venv\Scripts\pip install ultralytics>=8.0.0
venv\Scripts\pip install torch>=1.9.0
venv\Scripts\pip install torchvision>=0.10.0

# OCR依賴（已安裝）
venv\Scripts\pip install paddleocr --no-deps
```

### 5. 驗證安裝

運行測試腳本確認安裝成功：

```bash
venv\Scripts\python test_basic_imports.py
venv\Scripts\python test_system.py
```

## 使用方法

### 1. 命令行界面

```bash
# 激活虛擬環境
venv\Scripts\activate

# 識別單張圖像
python main.py -i image.jpg

# 批量識別
python main.py -b image1.jpg image2.jpg image3.jpg

# 處理文件夾
python main.py -f ./images/

# 使用攝像頭
python main.py -c
```

### 2. 圖形界面

```bash
venv\Scripts\activate
python interface\gui_app.py
```

### 3. 網頁界面

```bash
venv\Scripts\activate
python interface\web_app.py
```

然後訪問 http://localhost:5000

## 測試系統

我們已經創建了測試圖像和測試腳本：

```bash
# 創建測試圖像
venv\Scripts\python create_realistic_test.py

# 運行簡單測試
venv\Scripts\python simple_test.py

# 測試備用檢測方法
venv\Scripts\python test_fallback_detection.py
```

## 安裝狀態檢查

運行以下命令檢查當前安裝狀態：

```bash
venv\Scripts\python test_basic_imports.py
```

預期輸出：
```
測試基本導入...
OK OpenCV 4.10.0
OK NumPy 2.2.6
OK Pillow
OK Flask 3.1.2
OK Matplotlib
OK Pandas 2.3.2

測試深度學習庫...
OK PyTorch 2.8.0+cpu
OK Ultralytics (YOLO)
ERROR PaddleOCR: No module named 'paddlex'

基本導入測試完成！
```

**注意**: PaddleOCR顯示錯誤是正常的，因為我們使用了 `--no-deps` 安裝以避免依賴衝突。

## 故障排除

### 1. 安裝超時

如果安裝過程超時，可以分步安裝：

```bash
# 先安裝核心庫
venv\Scripts\pip install opencv-python numpy pillow

# 再安裝機器學習庫
venv\Scripts\pip install torch torchvision ultralytics

# 最後安裝其他庫
venv\Scripts\pip install flask matplotlib pandas
```

### 2. 字符編碼問題

如果遇到Unicode錯誤，請確保系統使用UTF-8編碼：

**Windows:**
```bash
chcp 65001
set PYTHONIOENCODING=utf-8
```

### 3. 內存不足

如果遇到內存不足問題：

1. 減小批量處理的圖像數量
2. 降低圖像分辨率
3. 使用更小的模型（YOLOv8n）

### 4. 檢測失敗

如果檢測失敗：

1. 確保圖像質量良好
2. 車牌清晰可見
3. 嘗試使用備用檢測方法
4. 調整置信度閾值

## 性能優化

### 1. 啟用GPU加速（如果可用）

```bash
# 安裝GPU版本的PyTorch
venv\Scripts\pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### 2. 調整參數

在 `utils/config.py` 中調整參數：

```python
# 降低置信度閾值以提高檢測率
CONFIDENCE_THRESHOLD = 0.3  # 原來是 0.5

# 調整圖像尺寸以加快速度
IMAGE_SIZE = (416, 416)  # 原來是 (640, 640)
```

## 系統架構

```
license_plate_recognition/
├── venv/                    # 虛擬環境
├── core/                    # 核心處理模組
├── interface/               # 用戶界面
├── utils/                   # 工具函數
├── test_samples/            # 測試圖像
├── data/                    # 數據存儲
├── tests/                   # 測試文件
├── main.py                  # 主程序入口
├── requirements_simple.txt  # 依賴列表
└── README.md               # 使用說明
```

## 聯繫支持

如果遇到問題：

1. 檢查錯誤信息和日誌
2. 運行測試腳本定位問題
3. 查看文檔和註釋
4. 確保所有依賴正確安裝

---

**恭喜！** 你的車牌辨識系統已經成功安裝並可以使用了！ 🎉