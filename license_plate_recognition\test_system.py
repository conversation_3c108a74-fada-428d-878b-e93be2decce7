# 測試車牌辨識系統
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("測試車牌辨識系統...")

try:
    from utils.config import Config
    print("OK 配置模組")
except ImportError as e:
    print(f"ERROR 配置模組: {e}")

try:
    from utils.image_utils import load_image, resize_image
    print("OK 圖像工具")
except ImportError as e:
    print(f"ERROR 圖像工具: {e}")

try:
    from core.preprocessor import ImagePreprocessor
    print("OK 預處理器")
except ImportError as e:
    print(f"ERROR 預處理器: {e}")

try:
    from core.detector import LicensePlateDetector
    print("OK 檢測器")
except ImportError as e:
    print(f"ERROR 檢測器: {e}")

try:
    from core.segmenter import CharacterSegmenter
    print("OK 分割器")
except ImportError as e:
    print(f"ERROR 分割器: {e}")

try:
    from core.recognizer import CharacterRecognizer
    print("OK 識別器")
except ImportError as e:
    print(f"ERROR 識別器: {e}")

try:
    from core.license_plate_recognition import LicensePlateRecognition
    print("OK 主識別類")
except ImportError as e:
    print(f"ERROR 主識別類: {e}")

print("\n系統測試完成！")