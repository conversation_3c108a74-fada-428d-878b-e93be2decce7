#!/usr/bin/env python3
"""
車牌檢測診斷腳本
用於診斷車牌檢測失敗的問題
"""

import cv2
import numpy as np
import sys
import os
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_image_loading(image_path):
    """測試圖像載入"""
    print(f"=== 測試圖像載入: {image_path} ===")
    
    # 檢查文件是否存在
    if not Path(image_path).exists():
        print(f"❌ 文件不存在: {image_path}")
        return None
    
    # 載入圖像
    image = cv2.imread(str(image_path))
    if image is None:
        print(f"❌ 無法載入圖像: {image_path}")
        return None
    
    print(f"✅ 圖像載入成功")
    print(f"   尺寸: {image.shape}")
    print(f"   類型: {image.dtype}")
    
    return image

def test_yolo_model():
    """測試YOLO模型載入"""
    print(f"\n=== 測試YOLO模型載入 ===")
    
    try:
        from ultralytics import YOLO
        print("✅ ultralytics 庫載入成功")
        
        # 嘗試載入YOLOv8n模型
        try:
            model = YOLO('yolov8n.pt')
            print("✅ YOLOv8n 模型載入成功")
            return model
        except Exception as e:
            print(f"❌ YOLOv8n 模型載入失敗: {e}")
            return None
            
    except ImportError as e:
        print(f"❌ ultralytics 庫未安裝: {e}")
        return None

def test_yolo_detection(model, image):
    """測試YOLO檢測"""
    print(f"\n=== 測試YOLO檢測 ===")
    
    if model is None or image is None:
        print("❌ 模型或圖像為空，跳過檢測")
        return []
    
    try:
        # 執行檢測
        results = model(image, conf=0.1, verbose=False)  # 降低置信度閾值
        print(f"✅ YOLO檢測執行成功")
        
        # 解析結果
        detections = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                print(f"   檢測到 {len(boxes)} 個物體")
                for i, box in enumerate(boxes):
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    detection = {
                        'bbox': (int(x1), int(y1), int(x2), int(y2)),
                        'confidence': float(confidence),
                        'class_id': class_id,
                        'class_name': model.names[class_id] if class_id < len(model.names) else 'unknown'
                    }
                    detections.append(detection)
                    
                    print(f"   檢測 {i+1}: {detection['class_name']} (置信度: {confidence:.3f})")
            else:
                print("   未檢測到任何物體")
        
        return detections
        
    except Exception as e:
        print(f"❌ YOLO檢測失敗: {e}")
        return []

def test_fallback_detection(image):
    """測試備用檢測方法"""
    print(f"\n=== 測試備用檢測方法 ===")
    
    if image is None:
        print("❌ 圖像為空，跳過檢測")
        return []
    
    try:
        # 轉換為灰度
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        print("✅ 轉換為灰度圖像")
        
        # 應用高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        print("✅ 應用高斯模糊")
        
        # 邊緣檢測
        edges = cv2.Canny(blurred, 50, 150)
        print("✅ 邊緣檢測完成")
        
        # 尋找輪廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"✅ 找到 {len(contours)} 個輪廓")
        
        # 過濾輪廓
        detections = []
        for i, contour in enumerate(contours):
            # 獲取邊界框
            x, y, w, h = cv2.boundingRect(contour)
            
            # 計算面積和寬高比
            area = cv2.contourArea(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            print(f"   輪廓 {i+1}: 位置=({x},{y}), 尺寸=({w}x{h}), 面積={area:.0f}, 寬高比={aspect_ratio:.2f}")
            
            # 應用過濾條件
            if w >= 50 and h >= 20 and area >= 500 and 1.5 <= aspect_ratio <= 6.0:
                detection = {
                    'bbox': (x, y, x + w, y + h),
                    'confidence': 0.5,
                    'area': area,
                    'aspect_ratio': aspect_ratio
                }
                detections.append(detection)
                print(f"     ✅ 符合車牌條件")
            else:
                print(f"     ❌ 不符合車牌條件")
        
        print(f"✅ 備用檢測完成，找到 {len(detections)} 個候選車牌")
        return detections
        
    except Exception as e:
        print(f"❌ 備用檢測失敗: {e}")
        return []

def visualize_detections(image, detections, title="檢測結果"):
    """可視化檢測結果"""
    if image is None or not detections:
        return
    
    # 複製圖像
    vis_image = image.copy()
    
    # 繪製檢測框
    for i, detection in enumerate(detections):
        x1, y1, x2, y2 = detection['bbox']
        confidence = detection.get('confidence', 0)
        
        # 繪製邊界框
        cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # 添加標籤
        label = f"{i+1}: {confidence:.3f}"
        cv2.putText(vis_image, label, (x1, y1-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 保存結果圖像
    output_path = f"debug_{title.replace(' ', '_')}.jpg"
    cv2.imwrite(output_path, vis_image)
    print(f"✅ 檢測結果已保存到: {output_path}")

def main():
    """主函數"""
    print("🔍 車牌檢測診斷工具")
    print("=" * 50)
    
    # 測試圖像路徑
    test_image_path = "test_samples/letters_only.jpg"
    
    # 1. 測試圖像載入
    image = test_image_loading(test_image_path)
    
    # 2. 測試YOLO模型
    model = test_yolo_model()
    
    # 3. 測試YOLO檢測
    yolo_detections = test_yolo_detection(model, image)
    if yolo_detections:
        visualize_detections(image, yolo_detections, "YOLO檢測")
    
    # 4. 測試備用檢測
    fallback_detections = test_fallback_detection(image)
    if fallback_detections:
        visualize_detections(image, fallback_detections, "備用檢測")
    
    # 5. 總結
    print(f"\n=== 診斷總結 ===")
    print(f"YOLO檢測結果: {len(yolo_detections)} 個")
    print(f"備用檢測結果: {len(fallback_detections)} 個")
    
    if not yolo_detections and not fallback_detections:
        print("❌ 所有檢測方法都未找到車牌")
        print("可能的原因:")
        print("1. 圖像中沒有明顯的車牌")
        print("2. 車牌太小或模糊")
        print("3. 檢測參數需要調整")
        print("4. 需要專用的車牌檢測模型")
    else:
        print("✅ 至少有一種方法檢測到了候選區域")

if __name__ == "__main__":
    main()
