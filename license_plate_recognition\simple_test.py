# 簡化測試腳本
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.license_plate_recognition import LicensePlateRecognition

print("初始化識別器...")
lpr = LicensePlateRecognition(save_results=False)  # 不保存結果以提高速度

# 測試圖像
test_images = [
    "test_samples/plate_closeup.jpg",
    "test_samples/realistic_car.jpg", 
    "test_samples/perspective_plate.jpg"
]

print("開始識別測試...")
for image_path in test_images:
    if os.path.exists(image_path):
        print(f"\n處理: {image_path}")
        try:
            result = lpr.recognize_from_image(image_path)
            
            if result['success']:
                print(f"  識別成功!")
                print(f"  車牌號碼: {result['plate_text']}")
                print(f"  置信度: {result['confidence']:.2%}")
                print(f"  處理時間: {result['processing_time']:.3f}秒")
                
                # 顯示檢測到的車牌數量
                print(f"  檢測到車牌: {len(result['detections'])}個")
                
            else:
                print(f"  識別失敗: {result.get('error', '未知錯誤')}")
                
        except Exception as e:
            print(f"  處理錯誤: {e}")
    else:
        print(f"  文件不存在: {image_path}")

# 顯示統計信息
stats = lpr.get_statistics()
print(f"\n統計信息:")
print(f"  總圖像數: {stats['total_images']}")
print(f"  成功檢測: {stats['successful_detections']} ({stats['detection_rate']:.1%})")
print(f"  成功識別: {stats['successful_recognitions']} ({stats['recognition_rate']:.1%})")
print(f"  平均處理時間: {stats['average_processing_time']:.3f}秒")