import cv2
import numpy as np
from PIL import Image, ImageEnhance
import os
from pathlib import Path

def load_image(image_path):
    """載入圖像文件"""
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"圖像文件不存在: {image_path}")
    
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"無法載入圖像: {image_path}")
    
    return image

def resize_image(image, target_size=(640, 640), maintain_ratio=True):
    """調整圖像尺寸"""
    if maintain_ratio:
        h, w = image.shape[:2]
        scale = min(target_size[0]/w, target_size[1]/h)
        new_w, new_h = int(w*scale), int(h*scale)
        resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        
        # 創建畫布並居中放置
        canvas = np.zeros((target_size[1], target_size[0], 3), dtype=np.uint8)
        y_offset = (target_size[1] - new_h) // 2
        x_offset = (target_size[0] - new_w) // 2
        canvas[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
        return canvas
    else:
        return cv2.resize(image, target_size, interpolation=cv2.INTER_LINEAR)

def enhance_image(image):
    """增強圖像質量"""
    # 轉換為PIL格式進行增強
    pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    
    # 增強對比度
    enhancer = ImageEnhance.Contrast(pil_image)
    enhanced = enhancer.enhance(1.2)
    
    # 增強銳度
    enhancer = ImageEnhance.Sharpness(enhanced)
    enhanced = enhancer.enhance(1.1)
    
    # 轉換回OpenCV格式
    return cv2.cvtColor(np.array(enhanced), cv2.COLOR_RGB2BGR)

def denoise_image(image):
    """圖像降噪"""
    # 使用高斯模糊降噪
    return cv2.GaussianBlur(image, (5, 5), 0)

def adjust_brightness_contrast(image, brightness=0, contrast=0):
    """調整亮度和對比度"""
    if brightness != 0:
        if brightness > 0:
            shadow = brightness
            highlight = 255
        else:
            shadow = 0
            highlight = 255 + brightness
        alpha_b = (highlight - shadow) / 255
        gamma_b = shadow
        
        buf = cv2.addWeighted(image, alpha_b, image, 0, gamma_b)
    else:
        buf = image.copy()
    
    if contrast != 0:
        f = 131 * (contrast + 127) / (127 * (131 - contrast))
        alpha_c = f
        gamma_c = 127 * (1 - f)
        
        buf = cv2.addWeighted(buf, alpha_c, buf, 0, gamma_c)
    
    return buf

def convert_to_grayscale(image):
    """轉換為灰度圖像"""
    return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

def apply_threshold(image, method='adaptive'):
    """應用閾值處理"""
    if method == 'adaptive':
        return cv2.adaptiveThreshold(image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 11, 2)
    elif method == 'otsu':
        _, thresh = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        return thresh
    else:
        _, thresh = cv2.threshold(image, 127, 255, cv2.THRESH_BINARY)
        return thresh

def save_image(image, output_path):
    """保存圖像"""
    cv2.imwrite(str(output_path), image)

def get_image_info(image_path):
    """獲取圖像信息"""
    image = load_image(image_path)
    h, w = image.shape[:2]
    file_size = os.path.getsize(image_path)
    
    return {
        'width': w,
        'height': h,
        'channels': image.shape[2] if len(image.shape) > 2 else 1,
        'file_size': file_size,
        'format': Path(image_path).suffix.lower()
    }