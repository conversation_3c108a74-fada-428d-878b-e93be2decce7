import cv2
import numpy as np
import torch
from pathlib import Path
from utils.config import Config
from core.preprocessor import ImagePreprocessor

class LicensePlateDetector:
    """車牌檢測器類"""
    
    def __init__(self, model_path=None, confidence_threshold=Config.CONFIDENCE_THRESHOLD):
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = Config.NMS_THRESHOLD
        self.preprocessor = ImagePreprocessor()
        
        # 設置模型路徑
        if model_path is None:
            self.model_path = Config.YOLO_MODEL_PATH
        else:
            self.model_path = Path(model_path)
        
        # 載入模型
        self.model = self._load_model()
        
    def _load_model(self):
        """載入YOLO模型"""
        try:
            from ultralytics import YOLO
            
            if self.model_path.exists():
                model = YOLO(str(self.model_path))
            else:
                # 使用預訓練的YOLOv8n模型
                model = YOLO('yolov8n.pt')
                print(f"警告: 未找到專用車牌檢測模型，使用通用YOLOv8n模型")
            
            return model
            
        except ImportError:
            print("錯誤: 請安裝ultralytics庫: pip install ultralytics")
            return None
        except Exception as e:
            print(f"載入模型時出錯: {e}")
            return None
    
    def detect(self, image):
        """檢測圖像中的車牌"""
        if self.model is None:
            return self._fallback_detection(image)

        try:
            # 預處理圖像
            processed_image = self.preprocessor.preprocess_for_detection(image)

            # 執行檢測
            results = self.model(processed_image, conf=self.confidence_threshold)

            # 解析檢測結果
            detections = self._parse_results(results, image.shape)

            # 如果YOLO沒有找到合適的檢測結果，使用備用方法
            if not detections:
                print("YOLO未檢測到車牌，使用備用檢測方法")
                fallback_detections = self._fallback_detection(image)
                return fallback_detections

            return detections

        except Exception as e:
            print(f"檢測過程中出錯: {e}")
            return self._fallback_detection(image)
    
    def _parse_results(self, results, original_shape):
        """解析YOLO檢測結果"""
        detections = []

        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for i, box in enumerate(boxes):
                    # 獲取邊界框座標
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())

                    # 過濾低置信度檢測
                    if confidence < self.confidence_threshold:
                        continue

                    # 檢查是否為車牌相關類別
                    # 由於使用通用YOLO模型，我們需要檢查可能包含車牌的物體
                    class_name = self.model.names[class_id] if hasattr(self.model, 'names') else 'unknown'

                    # 計算檢測區域的特徵
                    width = x2 - x1
                    height = y2 - y1
                    aspect_ratio = width / height if height > 0 else 0

                    # 如果沒有專用車牌檢測模型，使用更寬鬆的過濾條件
                    # 檢查檢測區域是否符合車牌的基本特徵
                    is_plate_like = (
                        2.0 <= aspect_ratio <= 6.0 and  # 車牌寬高比
                        width >= 30 and                  # 最小寬度
                        height >= 15 and                 # 最小高度
                        width * height >= 500            # 最小面積
                    )

                    # 如果檢測到的物體不符合車牌特徵，跳過
                    if not is_plate_like:
                        continue

                    # 確保座標在圖像範圍內
                    x1 = max(0, int(x1))
                    y1 = max(0, int(y1))
                    x2 = min(original_shape[1], int(x2))
                    y2 = min(original_shape[0], int(y2))

                    detection = {
                        'bbox': (x1, y1, x2, y2),
                        'confidence': float(confidence),
                        'class': 'license_plate',
                        'original_class': class_name,
                        'area': (x2 - x1) * (y2 - y1)
                    }

                    detections.append(detection)
        
        # 應用非極大值抑制
        detections = self._apply_nms(detections)
        
        return detections
    
    def _apply_nms(self, detections):
        """應用非極大值抑制"""
        if len(detections) <= 1:
            return detections
        
        # 提取邊界框和置信度
        bboxes = []
        confidences = []
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            bboxes.append([x1, y1, x2, y2])
            confidences.append(detection['confidence'])
        
        # 應用NMS
        indices = cv2.dnn.NMSBoxes(bboxes, confidences, 
                                 self.confidence_threshold, 
                                 self.nms_threshold)
        
        # 篩選結果
        filtered_detections = []
        if len(indices) > 0:
            for i in indices.flatten():
                filtered_detections.append(detections[i])
        
        return filtered_detections
    
    def _fallback_detection(self, image):
        """備用檢測方法（基於傳統圖像處理）"""
        try:
            # 轉換為灰度
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 應用高斯模糊
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # 邊緣檢測
            edges = cv2.Canny(blurred, 50, 150)
            
            # 尋找輪廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            detections = []
            
            for contour in contours:
                # 獲取邊界框
                x, y, w, h = cv2.boundingRect(contour)
                
                # 過濾尺寸
                if w < 100 or h < 30:
                    continue
                
                # 檢查寬高比
                aspect_ratio = w / h
                if aspect_ratio < 2.0 or aspect_ratio > 5.0:
                    continue
                
                # 計算面積
                area = cv2.contourArea(contour)
                if area < 1000:
                    continue
                
                detection = {
                    'bbox': (x, y, x + w, y + h),
                    'confidence': 0.5,  # 備用方法的置信度
                    'class': 'license_plate',
                    'area': area
                }
                
                detections.append(detection)
            
            return detections
            
        except Exception as e:
            print(f"備用檢測方法出錯: {e}")
            return []
    
    def batch_detect(self, image_paths):
        """批量檢測"""
        results = []
        
        for image_path in image_paths:
            try:
                image = cv2.imread(str(image_path))
                if image is None:
                    continue
                
                detections = self.detect(image)
                results.append({
                    'image_path': str(image_path),
                    'detections': detections,
                    'success': len(detections) > 0
                })
                
            except Exception as e:
                results.append({
                    'image_path': str(image_path),
                    'detections': [],
                    'success': False,
                    'error': str(e)
                })
        
        return results
    
    def get_detection_statistics(self, detections):
        """獲取檢測統計信息"""
        if not detections:
            return {
                'total_detections': 0,
                'average_confidence': 0,
                'average_area': 0
            }
        
        total_detections = len(detections)
        avg_confidence = sum(d['confidence'] for d in detections) / total_detections
        avg_area = sum(d['area'] for d in detections) / total_detections
        
        return {
            'total_detections': total_detections,
            'average_confidence': avg_confidence,
            'average_area': avg_area
        }