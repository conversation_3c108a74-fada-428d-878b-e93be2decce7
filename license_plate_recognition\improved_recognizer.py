#!/usr/bin/env python3
"""
改進的字符識別器
使用更精確的特徵分析來識別車牌字符
"""

import cv2
import numpy as np
import sys
import os
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.license_plate_recognition import LicensePlateRecognition

class ImprovedCharacterRecognizer:
    """改進的字符識別器"""
    
    def __init__(self):
        self.char_templates = self._create_character_templates()
    
    def _create_character_templates(self):
        """創建字符模板特徵"""
        templates = {}
        
        # 數字特徵 (基於形狀特徵)
        templates['0'] = {'holes': 1, 'vertical_lines': 2, 'horizontal_lines': 2, 'curves': 4}
        templates['1'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 0, 'curves': 0}
        templates['2'] = {'holes': 0, 'vertical_lines': 0, 'horizontal_lines': 3, 'curves': 2}
        templates['3'] = {'holes': 0, 'vertical_lines': 0, 'horizontal_lines': 2, 'curves': 2}
        templates['4'] = {'holes': 0, 'vertical_lines': 2, 'horizontal_lines': 1, 'curves': 0}
        templates['5'] = {'holes': 0, 'vertical_lines': 0, 'horizontal_lines': 3, 'curves': 1}
        templates['6'] = {'holes': 1, 'vertical_lines': 1, 'horizontal_lines': 2, 'curves': 3}
        templates['7'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 1, 'curves': 0}
        templates['8'] = {'holes': 2, 'vertical_lines': 2, 'horizontal_lines': 3, 'curves': 4}
        templates['9'] = {'holes': 1, 'vertical_lines': 1, 'horizontal_lines': 2, 'curves': 3}
        
        # 字母特徵
        templates['A'] = {'holes': 1, 'vertical_lines': 2, 'horizontal_lines': 1, 'curves': 0}
        templates['B'] = {'holes': 2, 'vertical_lines': 1, 'horizontal_lines': 3, 'curves': 2}
        templates['C'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 2, 'curves': 2}
        templates['D'] = {'holes': 1, 'vertical_lines': 1, 'horizontal_lines': 2, 'curves': 2}
        templates['E'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 3, 'curves': 0}
        templates['F'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 2, 'curves': 0}
        templates['G'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 3, 'curves': 2}
        templates['H'] = {'holes': 0, 'vertical_lines': 2, 'horizontal_lines': 1, 'curves': 0}
        templates['I'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 2, 'curves': 0}
        templates['J'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 1, 'curves': 1}
        templates['K'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 0, 'curves': 0}
        templates['L'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 1, 'curves': 0}
        templates['M'] = {'holes': 0, 'vertical_lines': 2, 'horizontal_lines': 0, 'curves': 0}
        templates['N'] = {'holes': 0, 'vertical_lines': 2, 'horizontal_lines': 0, 'curves': 0}
        templates['O'] = {'holes': 1, 'vertical_lines': 2, 'horizontal_lines': 2, 'curves': 4}
        templates['P'] = {'holes': 1, 'vertical_lines': 1, 'horizontal_lines': 2, 'curves': 1}
        templates['Q'] = {'holes': 1, 'vertical_lines': 2, 'horizontal_lines': 2, 'curves': 4}
        templates['R'] = {'holes': 1, 'vertical_lines': 1, 'horizontal_lines': 2, 'curves': 1}
        templates['S'] = {'holes': 0, 'vertical_lines': 0, 'horizontal_lines': 2, 'curves': 2}
        templates['T'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 1, 'curves': 0}
        templates['U'] = {'holes': 0, 'vertical_lines': 2, 'horizontal_lines': 1, 'curves': 2}
        templates['V'] = {'holes': 0, 'vertical_lines': 2, 'horizontal_lines': 0, 'curves': 0}
        templates['W'] = {'holes': 0, 'vertical_lines': 2, 'horizontal_lines': 0, 'curves': 0}
        templates['X'] = {'holes': 0, 'vertical_lines': 0, 'horizontal_lines': 0, 'curves': 0}
        templates['Y'] = {'holes': 0, 'vertical_lines': 1, 'horizontal_lines': 0, 'curves': 0}
        templates['Z'] = {'holes': 0, 'vertical_lines': 0, 'horizontal_lines': 2, 'curves': 0}
        
        return templates
    
    def analyze_character(self, char_img):
        """分析字符特徵"""
        try:
            # 預處理
            if len(char_img.shape) == 3:
                gray = cv2.cvtColor(char_img, cv2.COLOR_BGR2GRAY)
            else:
                gray = char_img.copy()
            
            # 二值化
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
            
            # 分析特徵
            features = {}
            
            # 1. 計算孔洞數量
            features['holes'] = self._count_holes(binary)
            
            # 2. 計算線條特徵
            features['vertical_lines'] = self._count_vertical_lines(binary)
            features['horizontal_lines'] = self._count_horizontal_lines(binary)
            
            # 3. 計算曲線特徵
            features['curves'] = self._count_curves(binary)
            
            # 4. 計算寬高比
            h, w = binary.shape
            features['aspect_ratio'] = w / h if h > 0 else 1
            
            # 5. 計算密度
            features['density'] = np.sum(binary > 0) / (w * h) if w * h > 0 else 0
            
            return features
            
        except Exception as e:
            print(f"特徵分析錯誤: {e}")
            return {'holes': 0, 'vertical_lines': 0, 'horizontal_lines': 0, 'curves': 0, 'aspect_ratio': 1, 'density': 0}
    
    def _count_holes(self, binary):
        """計算孔洞數量"""
        try:
            # 尋找輪廓
            contours, hierarchy = cv2.findContours(binary, cv2.RETR_CCOMP, cv2.CHAIN_APPROX_SIMPLE)
            
            if hierarchy is None:
                return 0
            
            # 計算內部輪廓（孔洞）
            holes = 0
            for i, h in enumerate(hierarchy[0]):
                if h[3] != -1:  # 有父輪廓，說明是孔洞
                    area = cv2.contourArea(contours[i])
                    if area > 50:  # 過濾小噪點
                        holes += 1
            
            return holes
        except:
            return 0
    
    def _count_vertical_lines(self, binary):
        """計算垂直線條數量"""
        try:
            # 垂直邊緣檢測
            kernel = np.array([[-1, 2, -1]], dtype=np.float32)
            vertical_edges = cv2.filter2D(binary, -1, kernel.T)
            
            # 統計強邊緣
            strong_edges = vertical_edges > 100
            return min(np.sum(strong_edges) // 20, 3)  # 限制最大值
        except:
            return 0
    
    def _count_horizontal_lines(self, binary):
        """計算水平線條數量"""
        try:
            # 水平邊緣檢測
            kernel = np.array([[-1], [2], [-1]], dtype=np.float32)
            horizontal_edges = cv2.filter2D(binary, -1, kernel)
            
            # 統計強邊緣
            strong_edges = horizontal_edges > 100
            return min(np.sum(strong_edges) // 20, 3)  # 限制最大值
        except:
            return 0
    
    def _count_curves(self, binary):
        """計算曲線特徵"""
        try:
            # 使用Canny邊緣檢測
            edges = cv2.Canny(binary, 50, 150)
            
            # 尋找輪廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            curves = 0
            for contour in contours:
                # 計算輪廓的凸包
                hull = cv2.convexHull(contour)
                hull_area = cv2.contourArea(hull)
                contour_area = cv2.contourArea(contour)
                
                if hull_area > 0:
                    # 凸性缺陷表示曲線
                    convexity = contour_area / hull_area
                    if convexity < 0.9:  # 有明顯凹陷
                        curves += 1
            
            return min(curves, 4)  # 限制最大值
        except:
            return 0
    
    def recognize_character(self, char_img):
        """識別單個字符"""
        features = self.analyze_character(char_img)
        
        best_char = 'A'
        best_score = 0
        
        for char, template in self.char_templates.items():
            score = self._calculate_similarity(features, template)
            if score > best_score:
                best_score = score
                best_char = char
        
        return {
            'character': best_char,
            'confidence': best_score,
            'method': 'improved_template',
            'features': features
        }
    
    def _calculate_similarity(self, features, template):
        """計算特徵相似度"""
        try:
            score = 0
            total_weight = 0
            
            # 權重設定
            weights = {
                'holes': 3.0,
                'vertical_lines': 2.0,
                'horizontal_lines': 2.0,
                'curves': 1.5
            }
            
            for feature, weight in weights.items():
                if feature in features and feature in template:
                    # 計算特徵差異
                    diff = abs(features[feature] - template[feature])
                    # 轉換為相似度分數
                    similarity = max(0, 1 - diff / 3.0)  # 歸一化
                    score += similarity * weight
                    total_weight += weight
            
            # 歸一化分數
            if total_weight > 0:
                score = score / total_weight
            
            return score
        except:
            return 0.0

def test_improved_recognizer():
    """測試改進的識別器"""
    print("🔧 測試改進的字符識別器")
    print("=" * 50)
    
    # 載入圖像
    image_path = "test_samples/simple_plate.jpg"
    image = cv2.imread(image_path)
    
    if image is None:
        print("❌ 無法載入圖像")
        return
    
    # 使用現有系統進行檢測和分割
    lpr = LicensePlateRecognition(save_results=False)
    
    # 檢測車牌
    detections = lpr.detector.detect(image)
    if not detections:
        print("❌ 未檢測到車牌")
        return
    
    # 提取車牌區域
    best_detection = detections[0]
    x1, y1, x2, y2 = best_detection['bbox']
    plate_region = image[y1:y2, x1:x2]
    
    # 預處理
    processed_plate = lpr.preprocessor.preprocess_for_ocr(plate_region)
    
    # 字符分割
    characters = lpr.segmenter.segment_characters(processed_plate)
    
    print(f"檢測到 {len(characters)} 個字符")
    
    # 使用改進的識別器
    improved_recognizer = ImprovedCharacterRecognizer()
    
    results = []
    for i, char_img in enumerate(characters):
        result = improved_recognizer.recognize_character(char_img)
        result['position'] = i
        results.append(result)
        
        print(f"字符 {i+1}: '{result['character']}' (置信度: {result['confidence']:.3f})")
        print(f"  特徵: {result['features']}")
    
    # 組合結果
    plate_text = ''.join([r['character'] for r in results])
    print(f"\n識別結果: '{plate_text}'")
    print(f"期望結果: 'ABC-123'")
    print(f"匹配度: {'✅ 完全匹配' if plate_text == 'ABC123' else '❌ 不匹配'}")

if __name__ == "__main__":
    test_improved_recognizer()
