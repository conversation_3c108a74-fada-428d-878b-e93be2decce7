#!/usr/bin/env python3
"""
完整車牌識別流程診斷腳本
"""

import cv2
import numpy as np
import sys
import os
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.license_plate_recognition import LicensePlateRecognition

def debug_full_pipeline(image_path):
    """調試完整的車牌識別流程"""
    print(f"🔍 調試完整車牌識別流程")
    print(f"圖像路徑: {image_path}")
    print("=" * 60)
    
    # 創建識別器
    lpr = LicensePlateRecognition(save_results=False)
    
    # 載入圖像
    image = cv2.imread(str(image_path))
    if image is None:
        print(f"❌ 無法載入圖像: {image_path}")
        return
    
    print(f"✅ 圖像載入成功: {image.shape}")
    
    try:
        # 執行識別並捕獲詳細信息
        result = lpr._recognize_image(image, image_path)
        
        print(f"\n=== 識別結果 ===")
        print(f"成功: {result.get('success', False)}")
        print(f"檢測數量: {len(result.get('detections', []))}")
        print(f"車牌文字: '{result.get('plate_text', '')}'")
        print(f"置信度: {result.get('confidence', 0):.3f}")
        print(f"錯誤: {result.get('error', '無')}")
        
        print(f"\n=== 處理階段 ===")
        stages = result.get('processing_stages', {})
        for stage, value in stages.items():
            print(f"{stage}: {value}")
        
        print(f"\n=== 檢測詳情 ===")
        detections = result.get('detections', [])
        for i, detection in enumerate(detections):
            print(f"檢測 {i+1}:")
            print(f"  邊界框: {detection.get('bbox')}")
            print(f"  置信度: {detection.get('confidence', 0):.3f}")
            print(f"  面積: {detection.get('area', 0)}")
        
        print(f"\n=== 識別詳情 ===")
        recognition_results = result.get('recognition_results', [])
        for i, rec in enumerate(recognition_results):
            print(f"字符 {i+1}:")
            print(f"  字符: '{rec.get('character', '')}'")
            print(f"  置信度: {rec.get('confidence', 0):.3f}")
            print(f"  位置: {rec.get('position', 0)}")
        
        # 如果有檢測結果，保存可視化圖像
        if detections:
            vis_image = image.copy()
            for detection in detections:
                x1, y1, x2, y2 = detection['bbox']
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(vis_image, f"{detection.get('confidence', 0):.3f}", 
                           (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            cv2.imwrite("debug_full_detection.jpg", vis_image)
            print(f"\n✅ 檢測可視化已保存到: debug_full_detection.jpg")
        
        return result
        
    except Exception as e:
        print(f"❌ 識別過程中出錯: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_individual_components(image_path):
    """測試各個組件"""
    print(f"\n🔧 測試各個組件")
    print("=" * 60)
    
    # 載入圖像
    image = cv2.imread(str(image_path))
    if image is None:
        print(f"❌ 無法載入圖像")
        return
    
    # 測試檢測器
    print(f"\n--- 測試檢測器 ---")
    try:
        from core.detector import LicensePlateDetector
        detector = LicensePlateDetector()
        detections = detector.detect(image)
        print(f"檢測結果: {len(detections)} 個")
        for i, det in enumerate(detections):
            print(f"  檢測 {i+1}: {det}")
    except Exception as e:
        print(f"檢測器錯誤: {e}")
    
    # 如果有檢測結果，測試後續組件
    if 'detections' in locals() and detections:
        best_detection = detections[0]
        x1, y1, x2, y2 = best_detection['bbox']
        plate_region = image[y1:y2, x1:x2]
        
        print(f"\n--- 測試預處理器 ---")
        try:
            from core.preprocessor import ImagePreprocessor
            preprocessor = ImagePreprocessor()
            processed_plate = preprocessor.preprocess_for_ocr(plate_region)
            print(f"預處理成功: {processed_plate.shape}")
            cv2.imwrite("debug_preprocessed.jpg", processed_plate)
        except Exception as e:
            print(f"預處理器錯誤: {e}")
        
        print(f"\n--- 測試字符分割器 ---")
        try:
            from core.segmenter import CharacterSegmenter
            segmenter = CharacterSegmenter()
            characters = segmenter.segment_characters(processed_plate)
            print(f"分割結果: {len(characters)} 個字符")
            for i, char in enumerate(characters):
                cv2.imwrite(f"debug_char_{i}.jpg", char)
        except Exception as e:
            print(f"分割器錯誤: {e}")
        
        print(f"\n--- 測試字符識別器 ---")
        try:
            from core.recognizer import CharacterRecognizer
            recognizer = CharacterRecognizer()
            if 'characters' in locals() and characters:
                recognition_results = recognizer.recognize_characters(characters)
                print(f"識別結果: {len(recognition_results)} 個")
                for result in recognition_results:
                    print(f"  {result}")
                
                plate_text = recognizer.combine_characters(recognition_results)
                print(f"組合結果: '{plate_text}'")
            else:
                print("沒有字符可供識別")
        except Exception as e:
            print(f"識別器錯誤: {e}")

def main():
    """主函數"""
    print("🔍 完整車牌識別流程診斷工具")
    print("=" * 60)
    
    # 測試圖像路徑
    test_image_path = "test_samples/simple_plate.jpg"
    
    # 1. 測試完整流程
    result = debug_full_pipeline(test_image_path)
    
    # 2. 測試各個組件
    test_individual_components(test_image_path)
    
    # 3. 總結
    print(f"\n=== 診斷總結 ===")
    if result:
        if result.get('success'):
            print("✅ 識別成功")
        else:
            print("❌ 識別失敗")
            error = result.get('error', '未知錯誤')
            print(f"錯誤原因: {error}")
            
            # 分析失敗原因
            detections = result.get('detections', [])
            if not detections:
                print("建議: 檢測階段失敗，可能需要調整檢測參數或使用專用車牌檢測模型")
            elif not result.get('recognition_results'):
                print("建議: 字符識別階段失敗，可能需要改進OCR模型或預處理方法")
            else:
                print("建議: 檢查車牌格式驗證邏輯")
    else:
        print("❌ 流程執行失敗")

if __name__ == "__main__":
    main()
