
# 測試車牌辨識
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.license_plate_recognition import LicensePlateRecognition

# 初始化識別器
print("初始化識別器...")
lpr = LicensePlateRecognition(save_results=True)

# 測試圖像路徑
test_images = [
    "test_samples/simple_plate.jpg",
    "test_samples/complex_plate.jpg",
    "test_samples/numbers_only.jpg",
    "test_samples/letters_only.jpg"
]

print("開始識別測試...")
for image_path in test_images:
    if os.path.exists(image_path):
        print(f"\n處理: {image_path}")
        result = lpr.recognize_from_image(image_path)
        
        if result['success']:
            print(f"  識別成功!")
            print(f"  車牌號碼: {result['plate_text']}")
            print(f"  置信度: {result['confidence']:.2%}")
            print(f"  處理時間: {result['processing_time']:.3f}秒")
        else:
            print(f"  識別失敗: {result.get('error', '未知錯誤')}")
    else:
        print(f"  文件不存在: {image_path}")

# 顯示統計信息
stats = lpr.get_statistics()
print(f"\n統計信息:")
print(f"  總圖像數: {stats['total_images']}")
print(f"  成功檢測: {stats['successful_detections']} ({stats['detection_rate']:.1%})")
print(f"  成功識別: {stats['successful_recognitions']} ({stats['recognition_rate']:.1%})")
print(f"  平均處理時間: {stats['average_processing_time']:.3f}秒")
