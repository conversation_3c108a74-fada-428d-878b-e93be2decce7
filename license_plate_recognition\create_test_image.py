import cv2
import numpy as np
from pathlib import Path

# 創建測試圖像目錄
test_dir = Path("test_samples")
test_dir.mkdir(exist_ok=True)

# 創建不同類型的測試圖像
test_cases = [
    ("simple_plate", "ABC-123"),
    ("complex_plate", "XYZ-4567"),
    ("numbers_only", "123-456"),
    ("letters_only", "ABC-DEF")
]

print("創建測試圖像...")

for name, text in test_cases:
    # 創建圖像
    image = np.zeros((300, 500, 3), dtype=np.uint8)
    
    # 繪製車牌背景
    cv2.rectangle(image, (100, 120), (400, 180), (255, 255, 255), -1)
    
    # 添加文字
    cv2.putText(image, text, (150, 155), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 3)
    
    # 保存圖像
    image_path = test_dir / f"{name}.jpg"
    cv2.imwrite(str(image_path), image)
    print(f"創建: {image_path}")

print(f"測試圖像已保存到 {test_dir} 目錄")

# 創建一個簡單的測試腳本
test_script = f"""
# 測試車牌辨識
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.license_plate_recognition import LicensePlateRecognition

# 初始化識別器
print("初始化識別器...")
lpr = LicensePlateRecognition(save_results=True)

# 測試圖像路徑
test_images = [
    "test_samples/simple_plate.jpg",
    "test_samples/complex_plate.jpg",
    "test_samples/numbers_only.jpg",
    "test_samples/letters_only.jpg"
]

print("開始識別測試...")
for image_path in test_images:
    if os.path.exists(image_path):
        print(f"\\n處理: {{image_path}}")
        result = lpr.recognize_from_image(image_path)
        
        if result['success']:
            print(f"  識別成功!")
            print(f"  車牌號碼: {{result['plate_text']}}")
            print(f"  置信度: {{result['confidence']:.2%}}")
            print(f"  處理時間: {{result['processing_time']:.3f}}秒")
        else:
            print(f"  識別失敗: {{result.get('error', '未知錯誤')}}")
    else:
        print(f"  文件不存在: {{image_path}}")

# 顯示統計信息
stats = lpr.get_statistics()
print(f"\\n統計信息:")
print(f"  總圖像數: {{stats['total_images']}}")
print(f"  成功檢測: {{stats['successful_detections']}} ({{stats['detection_rate']:.1%}})")
print(f"  成功識別: {{stats['successful_recognitions']}} ({{stats['recognition_rate']:.1%}})")
print(f"  平均處理時間: {{stats['average_processing_time']:.3f}}秒")
"""

# 保存測試腳本
with open("test_demo.py", "w", encoding="utf-8") as f:
    f.write(test_script)

print("測試腳本已創建: test_demo.py")
print("\n運行以下命令開始測試:")
print("venv\\Scripts\\python test_demo.py")