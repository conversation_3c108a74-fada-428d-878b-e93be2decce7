import os
from pathlib import Path

class Config:
    BASE_DIR = Path(__file__).parent.parent
    
    # 模型文件路徑
    MODELS_DIR = BASE_DIR / "models"
    YOLO_MODEL_PATH = MODELS_DIR / "yolo_license_plate.pt"
    OCR_MODEL_PATH = MODELS_DIR / "ocr_model.pth"
    
    # 數據路徑
    DATA_DIR = BASE_DIR / "data"
    SAMPLES_DIR = DATA_DIR / "samples"
    RESULTS_DIR = DATA_DIR / "results"
    
    # 圖像處理參數
    IMAGE_SIZE = (640, 640)
    CONFIDENCE_THRESHOLD = 0.5
    NMS_THRESHOLD = 0.4
    
    # OCR參數
    OCR_CONFIDENCE = 0.6
    CHAR_WIDTH_MIN = 10
    CHAR_HEIGHT_MIN = 20
    
    # 車牌規則（台灣）
    LICENSE_PLATE_PATTERN = r'^[A-Z]{2,3}-\d{3,4}$|^[A-Z]\d{2}-[A-Z]{2}$'
    
    # 支持的文件格式
    SUPPORTED_IMAGE_FORMATS = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    SUPPORTED_VIDEO_FORMATS = {'.mp4', '.avi', '.mov', '.wmv'}
    
    @classmethod
    def ensure_directories(cls):
        """確保必要的目錄存在"""
        for dir_path in [cls.MODELS_DIR, cls.SAMPLES_DIR, cls.RESULTS_DIR]:
            dir_path.mkdir(parents=True, exist_ok=True)

# 初始化配置
Config.ensure_directories()