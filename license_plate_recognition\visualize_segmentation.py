#!/usr/bin/env python3
"""
可視化字符分割結果
"""

import cv2
import numpy as np
import sys
import os
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.license_plate_recognition import LicensePlateRecognition

def visualize_segmentation(image_path):
    """可視化字符分割過程"""
    print(f"🔍 可視化字符分割: {image_path}")
    print("=" * 50)
    
    # 載入圖像
    image = cv2.imread(image_path)
    if image is None:
        print("❌ 無法載入圖像")
        return
    
    print(f"✅ 原始圖像尺寸: {image.shape}")
    
    # 創建識別器
    lpr = LicensePlateRecognition(save_results=False)
    
    # 1. 檢測車牌
    print("\n--- 車牌檢測 ---")
    detections = lpr.detector.detect(image)
    if not detections:
        print("❌ 未檢測到車牌")
        return
    
    best_detection = detections[0]
    x1, y1, x2, y2 = best_detection['bbox']
    print(f"✅ 檢測到車牌: ({x1}, {y1}, {x2}, {y2})")
    
    # 2. 提取車牌區域
    plate_region = image[y1:y2, x1:x2]
    print(f"✅ 車牌區域尺寸: {plate_region.shape}")
    cv2.imwrite("debug_plate_region.jpg", plate_region)
    
    # 3. 預處理
    print("\n--- 圖像預處理 ---")
    processed_plate = lpr.preprocessor.preprocess_for_ocr(plate_region)
    print(f"✅ 預處理後尺寸: {processed_plate.shape}")
    cv2.imwrite("debug_processed_plate.jpg", processed_plate)
    
    # 4. 字符分割
    print("\n--- 字符分割 ---")
    characters = lpr.segmenter.segment_characters(processed_plate)
    print(f"✅ 分割出 {len(characters)} 個字符")
    
    # 保存每個字符
    for i, char in enumerate(characters):
        char_path = f"debug_char_{i}.jpg"
        cv2.imwrite(char_path, char)
        print(f"  字符 {i+1}: 尺寸 {char.shape}, 保存到 {char_path}")
    
    # 5. 創建分割可視化圖像
    print("\n--- 創建可視化 ---")
    
    # 在原始車牌上標記分割線
    vis_plate = cv2.cvtColor(processed_plate, cv2.COLOR_GRAY2BGR)
    
    # 使用分割器的內部方法來獲取分割位置
    try:
        # 重新執行分割以獲取分割位置
        segmenter = lpr.segmenter
        processed = segmenter._preprocess_plate(plate_region)
        
        # 垂直投影分割
        projection = np.sum(processed, axis=0)
        
        # 找到分割點
        split_points = []
        in_char = False
        char_start = 0
        
        for i, val in enumerate(projection):
            if val > 0 and not in_char:  # 進入字符
                char_start = i
                in_char = True
            elif val == 0 and in_char:  # 離開字符
                split_points.append((char_start, i))
                in_char = False
        
        if in_char:  # 最後一個字符
            split_points.append((char_start, len(projection)))
        
        print(f"✅ 找到 {len(split_points)} 個分割區域:")
        for i, (start, end) in enumerate(split_points):
            print(f"  區域 {i+1}: x={start}-{end}, 寬度={end-start}")
            
            # 在可視化圖像上畫線
            cv2.line(vis_plate, (start, 0), (start, vis_plate.shape[0]), (0, 255, 0), 1)
            cv2.line(vis_plate, (end, 0), (end, vis_plate.shape[0]), (0, 0, 255), 1)
            
            # 添加標籤
            cv2.putText(vis_plate, str(i+1), (start+5, 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
        
        cv2.imwrite("debug_segmentation_visualization.jpg", vis_plate)
        print(f"✅ 分割可視化保存到: debug_segmentation_visualization.jpg")
        
        # 6. 分析投影
        print(f"\n--- 垂直投影分析 ---")
        print(f"投影長度: {len(projection)}")
        print(f"非零值數量: {np.sum(projection > 0)}")
        print(f"最大投影值: {np.max(projection)}")
        print(f"平均投影值: {np.mean(projection):.2f}")
        
        # 保存投影圖
        proj_img = np.zeros((100, len(projection)), dtype=np.uint8)
        max_val = np.max(projection) if np.max(projection) > 0 else 1
        for i, val in enumerate(projection):
            height = int((val / max_val) * 90)
            if height > 0:
                proj_img[100-height:100, i] = 255
        
        cv2.imwrite("debug_projection.jpg", proj_img)
        print(f"✅ 投影圖保存到: debug_projection.jpg")
        
    except Exception as e:
        print(f"❌ 分割分析出錯: {e}")
    
    print(f"\n=== 總結 ===")
    print(f"期望字符數: 6 (A-B-C---1-2-3)")
    print(f"實際字符數: {len(characters)}")
    print(f"可能問題:")
    if len(characters) < 6:
        print("  - 字符分割不完整，可能某些字符被合併")
        print("  - 連字符'-'可能被忽略")
        print("  - 分割閾值可能需要調整")
    elif len(characters) > 6:
        print("  - 字符被過度分割")
        print("  - 可能包含噪點")

def main():
    """主函數"""
    image_path = "test_samples/simple_plate.jpg"
    visualize_segmentation(image_path)

if __name__ == "__main__":
    main()
